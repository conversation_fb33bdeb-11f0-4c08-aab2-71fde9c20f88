<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.0.0</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.tal.sea.seaover</groupId>
    <artifactId>edu-801-question</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>edu-801-question</name>
    <description>edu-801-question</description>
    <properties>
        <java.version>17</java.version>
        <spring-cloud-alibaba.version>2022.0.0.0-RC1</spring-cloud-alibaba.version>
        <spring-cloud.version>2022.0.0</spring-cloud.version>
        <mybatis-plus-starter.version>3.5.5</mybatis-plus-starter.version>
        <validation-api.version>2.0.0.Final</validation-api.version>
        <xpod-tools.version>0.1.0</xpod-tools.version>
        <rocketmq-client-java.version>5.0.4</rocketmq-client-java.version>
        <gson.version>2.10.1</gson.version>
        <springboot-validation.version>3.0.0</springboot-validation.version>
        <commons-lang3.version>3.4</commons-lang3.version>
        <joda.time.version>2.9.9</joda.time.version>
        <redisson.version>3.19.0</redisson.version>
        <okhttp3.version>4.10.0</okhttp3.version>
        <okhttp-sse.version>4.10.0</okhttp-sse.version>
    </properties>
    <modules>
        <module>application</module>
    </modules>
    <dependencies>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--mybatis-plus的springboot支持/ 低版本不支持springboot3 https://github.com/baomidou/mybatis-plus/issues/4971-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>${springboot-validation.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda.time.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tal.sea</groupId>
                <artifactId>xpod-301-tools</artifactId>
                <version>${xpod-tools.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp-sse</artifactId>
                <version>${okhttp-sse.version}</version>
            </dependency>

            <dependency>
                <groupId>com.microsoft.cognitiveservices.speech</groupId>
                <artifactId>client-sdk</artifactId>
                <version>1.38.0</version>
            </dependency>
            <dependency>
                <groupId>com.xesv5.dog</groupId>
                <artifactId>xiaotianquan-java-sdk</artifactId>
                <version>v1.2.2</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.1.0</version>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>aliyun</id>
            <name>aliyun</name>
            <url>https://maven.aliyun.com/repository/central</url>
        </repository>

        <repository>
            <id>nexus-tal-yach</id>
            <url>https://neuxs-yach.zhiyinlou.com/repository/sea-server/</url>
        </repository>
    </repositories>


</project>
