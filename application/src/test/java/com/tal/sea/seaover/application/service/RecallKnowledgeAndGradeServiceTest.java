package com.tal.sea.seaover.application.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tal.sea.seaover.application.dto.recall.RecallResult;
import com.tal.sea.seaover.application.entity.EduQuestion;
import com.tal.sea.seaover.application.mapper.EduQuestionMapper;
import com.tal.sea.seaover.application.service.data.RecallKnowledgeAndGradeService;
import com.tal.sea.seaover.application.service.data.SolutionValidationService;
import com.tal.sea.seaover.application.util.QuestionSolutionProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Date;
import java.util.List;

/**
 * RecallKnowledgeAndGradeService测试类
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("dev")
public class RecallKnowledgeAndGradeServiceTest {

    @Autowired
    private RecallKnowledgeAndGradeService recallService;

    @Autowired
    private SolutionValidationService solutionValidationService;
    @Autowired
    private InnerEduQuestionService innerEduQuestionService;

    @Test
    public void testRecall() {
        try {
            String questionContent = "What is the total number of sports balls collected by students in Class 3B for the school donation drive? A. 52 B. 65 C. 78 D. 92";

            RecallResult result = recallService.recall(questionContent);

            log.info("召回测试成功");
            log.info("年级: {}", result.getGrade());
            log.info("知识点名称: {}", result.getKnowledgeName());
            log.info("父知识点名称: {}", result.getParentKnowledgeName());
            log.info("知识点ID: {}", result.getKnowledgeId());

        } catch (Exception e) {
            log.error("召回测试失败", e);
        }
    }

    /**
     * 处理试题解析不符合规范的进行设置为无效
     * 分页查询未删除、有效的试题，验证solution格式，不符合规范的设置为不可用
     */
    @Test
    public void handleQuestionSolution() {
        try {
            log.info("开始执行试题solution验证任务");

            SolutionValidationService.SolutionValidationResult result =
                    solutionValidationService.handleQuestionSolution();

            log.info("试题solution验证任务执行完成");
            log.info("处理结果统计：");
            log.info("- 总处理数量: {}", result.getTotalProcessed());
            log.info("- 无效数量: {}", result.getTotalInvalid());
            log.info("- 已更新数量: {}", result.getTotalUpdated());
            log.info("- 有效率: {:.2f}%",
                    result.getTotalProcessed() > 0 ?
                    (double)(result.getTotalProcessed() - result.getTotalInvalid()) / result.getTotalProcessed() * 100 : 0);

        } catch (Exception e) {
            log.error("试题solution验证任务执行失败", e);
        }
    }

    /**
     * 处理试题解析不符合规范的进行设置为无效
     * 分页查询未删除、有效的试题，验证solution格式，不符合规范的设置为不可用
     */
    @Test
    public void cleanQuestionsWithBeginEndPairs() {
        try {
            innerEduQuestionService.cleanQuestionsWithBeginEndPairs();
        } catch (Exception e) {
            log.error("试题solution验证任务执行失败", e);
        }
    }
}