server:
  port: 9040

management:
  server:
    port: 9040
  metrics:
    tags:
      application: ${spring.application.name}
  endpoints:
    web:
      exposure:
        include: health,prometheus,metrics,loggers,info
  endpoint:
    health:
      show-details: always
  health:
    elasticsearch:
      enabled: false

logging:
  config: "classpath:log4j2-local.xml"
  level:
    com.zaxxer.hikari.pool.HikariPool: debug

spring:
  application:
    name: edu-801-question-application
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 200MB
      file-size-threshold: 10MB
      enabled: true
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchClientAutoConfiguration
      - org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration
      - org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration
      - org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchRepositoriesAutoConfiguration
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
  data:
    redis:
      host: 127.0.0.1
      port: 6379
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
        url: *********************************************************************************************************************************************************
        username: ai_tools_rw
        password: jJ9yO_uD7lS4dK0q
#    url: **********************************************************************************************************************
#    username: root
#    password: root111111
  elasticsearch:
    # ES7兼容配置 - 使用认证URL格式
    #uris: http://elastic:<EMAIL>:9200
    uris: http://es-cn-zvp2b7odq003pg8ep.elasticsearch.aliyuncs.com:9200
    username: elastic
    password: p6jYjyoOEsJXhOBp
    connection-timeout: 10s
    socket-timeout: 30s
    # ES7 REST客户端配置
    rest:
      connection-timeout: 10s
      read-timeout: 30s


#  datasource:
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: ***************************************************************************************************************************************************************
#    username: ai_tools_rw
#    password: jJ9yO_uD7lS4dK0q
question:
  es:
    index:
      name: overseas_edu_801_questions
  labeling:
    expectationSize: 10
    reportToBing: false
  subjects:
    list:
      - name: Math
        description: Algebra, Geometry, Calculus and more

sea:
  alarm:
    xiaotianquantaskid: 1463604
    xiaotianquantoken: f67297c33c6fd8fdef5db89900efd76449ca70f9

# Gemini AI配置
gemini:
  math:  # 数学模型配置组
    api-url: http://ai-service-test.tal.com/openai-compatible/v1/chat/completions
    model: gemini-2.5-flash-preview
    api-key: 300000182:ed2cf372e844d7699079de387e1b830f
    promotion-file: promotion/gemini-math-promotion_v2.txt
  comment:  # 评论模型配置组
    api-url: http://ai-service-test.tal.com/openai-compatible/v1/chat/completions
    model: gemini-2.5-flash-preview
    api-key: 300000269:1901f744e05c179756c9bf13af8c7555
    promotion-file: promotion/gemini-comment-promotion.txt
#doubao AI配置
doubao:
  api-url: http://ai-service-test.tal.com/openai-compatible/v1/chat/completions
  model: doubao-pro-32k
  api-key: 300000269:1901f744e05c179756c9bf13af8c7555
  promotion-file: promotion/doubao-math-promotion.txt

#openai:
#  api-url: http://ai-service.tal.com/openai-compatible/v1/chat/completions
#  model: gpt-4o
#  api-key: 1000080734:db6650536fad0230552699cab258b6e4

# 召回知识点和年级配置
recall:
  api-url: https://overseas-admin-api.chengjiukehu.com/recall
# asr的相关配置
video:
  speech:
    key: 6520764269dd46779d2af32070b0b117
    region: chinaeast2

#seoUrl接口相关配置
seo:
  url:
    pageSize: 500
    cacheExpireTime: 3600