You are an expert in meticulous format rewriting, skilled in formatting and typesetting mathematical problems. I will provide you with questions, and you need to process them around the [core principles] in accordance with the [Requirements for Standardizing Problem Processing
], and strictly output the data in JSON format as per the output example.

# Core Principles
- The content of the title itself shall not be altered in any way. No additions or reductions are allowed. Only format replacement processing is permitted.

# Requirements for Standardizing Problem Processing
1. LaTeX Formatting Rules
- Formula/Equation Enclosure
  - When including LaTeX formulas in your response, always enclose the formula between double dollar signs ($$).
    - Example: $$\\frac{3}{10}$$, $$70 \\div 10$$
  - Insert `\t\t` between two consecutive formulas to prevent them from merging into a single formula.
  - The symbol "$" representing the price should not be formatted in LaTeX.
- Rendering in LaTeX
  - Convert all mathematical expressions (e.g., squares, fractions, π, Mixed fractions) into proper LaTeX format.
    - Important: Only render actual exponents. Do not treat the digit "2" as an exponent if it is not meant to indicate a square.
    - Incorrect examples: x2+y2=5，1/10x1/2，pi，4 2/3
    - Correct examples: x^{2}+y^{2}=5，\\frac{1}{10} \\times \\frac{1}{2}，\\pi，4 \\frac{2}{3}
- JSON Escape Requirement
  - When returning LaTeX inside JSON, escape every backslash (\) by doubling it (\\).
  - Example:
    - LaTeX: $$\sin { \left( a+y \right) }$$
    - JSON-safe LaTeX: $$\\sin { \\left( a+y \\right) }$$
2. Answer Placeholder Replacement
- If the question contains brackets explicitly intended for filling in answers (e.g., “( )”), replace them with {answer_brackets}., underlines explicitly intended for filling in answers , replace them with {answer_underline}.
- Never create or guess placeholders on your own; if the original text does not contain brackets or underlines for answers, {answer_brackets} and {answer_underline} must not appear in the output.
3. Question Type Determination
- Classify the question into one of the following types:
  - Multiple Choice (type: 1)、Short Answer (type: 2)、True/False (type: 3)、Fill in the Blank (type: 4)、Others (type: 5)
- Any types that cannot be identified must be categorized as "Others" (type: 5).
4. Rules for inserting \n:
- Definition of “Question”
  - A "question" consists of a question stem and the question itself. Always insert `\n` after the question stem.
- Multiple Main Questions
  - If the text contains multiple main questions, insert `\n` after the end of each main question.
- Multiple Choice Questions
  - Insert \n after the question stem so that the options start on a new line.
  - If the total combined length of all options (including spaces) exceeds 80 characters, insert `\n` after each option. If there is no more than, append \t\t\t\t at the end of each option.
- Questions with Multiple Sub-questions
  - Insert `\n` after each sub-question.
- Other Cases
  - Do not insert `\n` in any other cases.

# 限制
- 不要输出题目解答的内容。
- 不能更改题目本身内容，即使它与题目无关。

# JSON格式检查
- 检查生成的JSON格式是否正确，不正确的JSON需要重新生成。

*** 示例 ***
- 输入1：
Solve the system of linear equations 3y - 2x = 11 y = 9 - 2x （）A.(-2,13)  B.(2,2)  C.(2,5)  D.(5,2)
- 输出1：
{
    "question": "Solve the system of linear equations $$3y - 2x = 11$$ \t\t $$y = 9 - 2x$$（）\nA.(-2,13)\t\t\t\tB.(2,2)\t\t\t\tC.(2,5)\t\t\t\tD.(5,2)",
    "type": 1
}


- 输入2：
Suppose you have the inequality 2x < 6. Determine the possible values of x. Explain your reasoning.
Michelle is 3 times as old as her sister Beth. For each question, write and solve an equation or inequality to describe Beth's possible ages. Then, graph the solution set on the number line. a. How old will Beth be when Michelle is at least 27 years old? b. How old will Beth be when Michelle is younger than 30 years old? c. How old will Beth be when Michelle is 42 years old?
- 输出2:
{
    "question": "Suppose you have the inequality $$2x < 6$$. Determine the possible values of x. Explain your reasoning.\nMichelle is 3 times as old as her sister Beth. For each question, write and solve an equation or inequality to describe Beth's possible ages. Then, graph the solution set on the number line.\na. How old will Beth be when Michelle is at least 27 years old?\n b. How old will Beth be when Michelle is younger than 30 years old?\n c. How old will Beth be when Michelle is 42 years old?\n",
    "type": 2
}


- 输入3：
what is 5/6 divided by 1/4
- 输出3：
{
  "question": "What is $$\\frac{5}{6}$$ divided by $$\\frac{1}{4}$$",
  "type": 2
}

- 输入4:
A firm sells a product in a purely competitive market. The marginal cost of the product at the current output level of 800 units is $3.50. The minimum possible average variable cost is $3. The market price of the product is $4. To maximize profits, the firm should  a. continue producing 800 units. b. decrease production to less than 800 units. c. increase production to more than 800 units. d. shut down.
- 输出4:
{
	"question": "A firm sells a product in a purely competitive market. The marginal cost of the product at the current output level of 800 units is $3.50. The minimum possible average variable cost is $3. The market price of the product is $4. To maximize profits, the firm should \na. continue producing 800 units.\nb. decrease production to less than 800 units.\nc. increase production to more than 800 units.\nd. shut down.",
	"type": 1
}