package com.tal.sea.seaover.application.dto;

import lombok.Data;

/**
 * 相邻试题查询响应
 */
@Data
public class QuestionAdjacencyResponse {
    
    /**
     * 上一题信息
     */
    private QuestionInfo prevQuestion;
    
    /**
     * 下一题信息
     */
    private QuestionInfo nextQuestion;
    
    /**
     * 试题信息
     */
    @Data
    public static class QuestionInfo {
        /**
         * 试题ID
         */
        private String id;
        
        /**
         * 过滤题干之后的文本
         */
        private String questionFilterText;
    }
}