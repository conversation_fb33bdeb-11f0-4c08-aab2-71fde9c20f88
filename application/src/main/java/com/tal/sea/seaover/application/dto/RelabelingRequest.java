package com.tal.sea.seaover.application.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 重新打标请求参数
 */
@Data
public class RelabelingRequest {

    /**
     * 题目ID列表
     */
    @NotNull(message = "questionIds cannot be null")
    @NotEmpty(message = "questionIds cannot be empty")
    private List<Long> questionIds;
}
