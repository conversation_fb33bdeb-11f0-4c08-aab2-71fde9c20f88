package com.tal.sea.seaover.application.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tal.sea.xpod.tools.util.JacksonBuilder;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.OkHttpClient.Builder;
import org.apache.commons.lang3.StringUtils;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLSession;
import java.io.IOException;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


@Slf4j
public class OkHttpHelperUtil {
    public static final Builder builder = new Builder();
    public static final Integer TIME_OUT = 30;
    private static ObjectMapper objectMapper = JacksonBuilder.getInstance();

    static {
        try {
            builder.connectTimeout(TIME_OUT, TimeUnit.SECONDS);
        } catch (IllegalArgumentException ignored) {
        }
        try {
            builder.writeTimeout(TIME_OUT, TimeUnit.SECONDS);
        } catch (IllegalArgumentException ignored) {
        }
        try {
            builder.readTimeout(TIME_OUT, TimeUnit.SECONDS);
        } catch (IllegalArgumentException ignored) {
        }
    }

    public static OkHttpClient createClient() {
        return builder.build();
    }

    public static OkHttpClient createClient(int timeout) {
        try {
            Builder builder = new Builder();
            builder.connectTimeout(timeout, TimeUnit.SECONDS);
            builder.readTimeout(timeout, TimeUnit.SECONDS);
            builder.writeTimeout(timeout, TimeUnit.SECONDS);
            return builder.build();
        } catch (Exception e) {
            return builder.build();
        }
    }

    public static Request buildPostRequest(String url, Map<String, Object> params, String apiSign, String timestamp, String contentType) {
        FormBody.Builder builder = new FormBody.Builder();
        if (params != null && !params.isEmpty()) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                builder.add(entry.getKey(), entry.getValue() != null ? entry.getValue().toString() : null);
            }
        }
        RequestBody formBody = builder.build();
        Request.Builder requestBuilder = new Request.Builder().url(url);
        requestBuilder.post(formBody);
        if (!StringUtils.isEmpty(apiSign)) {
            requestBuilder.addHeader("api-sign", apiSign);
        }
        if (!StringUtils.isEmpty(timestamp)) {
            requestBuilder.addHeader("timestamp", timestamp);
        }
        if (!StringUtils.isNotBlank(contentType)) {
            requestBuilder.addHeader("Content-Type", contentType);
        }
        Request request = requestBuilder.build();
        return request;
    }


    public static Request buildJsonPostRequest(String url, Map<String, Object> params, String cookies) throws IOException {
        return buildJsonPostRequest(url, params, cookies, null);
    }

    public static Request buildJsonPostRequest(String url, Map<String, Object> params, String cookies, String userAgent) throws IOException {
        MediaType JSON = MediaType.parse("application/json; charset=utf-8");
        String jsonParam = objectMapper.writeValueAsString(params);
//        RequestBody body = RequestBody.create(JSON, jsonParam);
        RequestBody body = RequestBody.create(jsonParam, JSON);
        Request.Builder requestBuilder = new Request.Builder().url(url);
        requestBuilder.post(body);
        if (!StringUtils.isEmpty(cookies)) {
            requestBuilder.addHeader("Cookie", cookies);
        }
        if (!StringUtils.isEmpty(userAgent)) {
            requestBuilder.addHeader("User-Agent", userAgent);
        }
        Request request = requestBuilder.build();
        return request;
    }

    /**
     * 构建Ok Http请求
     *
     * @param url            请求url
     * @param paramsTypeJson 请求参数，类型为Json
     */
    public static Request buildJsonPostRequest(String url, String paramsTypeJson) throws Exception {
        return buildJsonPostRequest(url, paramsTypeJson, null, null);
    }

    /**
     * 发送请求
     */
    public static JsonNode okHttpsRequest(Request okHttpRequest) throws IOException {
        Long startTime = System.currentTimeMillis() / 1000;
        int timeout = 120;
        // 构建https请求
        OkHttpClient client = OkHttpHelperUtil.builder.hostnameVerifier(new HostnameVerifier() {
            @Override
            public boolean verify(String string, SSLSession ssls) {
                return true;
            }
        }).readTimeout(timeout, TimeUnit.SECONDS).build();
        // 执行http请求
        Response response = client.newCall(okHttpRequest).execute();
        Long endTime = System.currentTimeMillis() / 1000;
        if (!response.isSuccessful()) {
            String respStr = getRespStr(response);
            throw new IOException("outsideService.okHttpsRequest 请求异常，错误码：" + response.code() + "｜" + response.message() + "|response:" + respStr);
        }
        // 应答报文
        String responseStr = Objects.requireNonNull(response.body()).string();
//        System.out.println("response:" + responseStr);
        return objectMapper.readTree(responseStr);
    }

    private static String getRespStr(Response execute) {
        if (execute == null) {
            return "";
        }
        try {
            return execute.body().string();
        } catch (Exception e) {
            return "";
        }
    }

    public static Request buildJsonRequest(String url, String paramsTypeJson, Map<String, String> headers) {
        if (StringUtils.isEmpty(url)) {
            throw new NullPointerException("请求URL不能为空URL不能为空");
        }
        // 封装请求体
        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), paramsTypeJson);
        // 构建请求
        Request.Builder requestBuilder = new Request.Builder().url(url);
        // 设置请求体
        requestBuilder.post(body);
        //设置请求头
        AddHeader(requestBuilder, headers);
        return requestBuilder.build();
    }

    public static Request AddHeader(Request.Builder request, Map<String, String> map) {
        // 默认设置请求头traceId
        String traceId = ThreadMdcUtil.getTraceId();
        if (StringUtils.isNotEmpty(traceId)) {
            request.addHeader("traceId", traceId);
        }
        if (map != null && !map.isEmpty()) {
            for (Map.Entry<String, String> entry : map.entrySet()) {
                request.addHeader(entry.getKey(), entry.getValue());
            }
        }
        return request.build();
    }

    /**
     * 构建OK HTTP请求
     *
     * @param url            请求url
     * @param paramsTypeJson 请求参数,类型为JSON
     */
    public static Request buildJsonPostRequest(String url, String paramsTypeJson, String cookie, String userAgent) throws Exception {

        if (StringUtils.isEmpty(url)) {
            throw new NullPointerException("请求URL不能为空URL不能为空");
        }

        // 封装请求体
        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), paramsTypeJson);
        // 构建请求
        Request.Builder requestBuilder = new Request.Builder().url(url);
        // 设置请求体
        requestBuilder.post(body);
        if (!StringUtils.isEmpty(cookie)) {
            requestBuilder.addHeader("Cookie", cookie);
        }
        if (!StringUtils.isEmpty(userAgent)) {
            requestBuilder.addHeader("User-Agent", userAgent);
        }
        return requestBuilder.build();
    }

    public static Request AddHeader(Request request, String headerKey, String headerValue) {
        if (request == null) {
            return request;
        }
        if (StringUtils.isAnyBlank(headerKey, headerValue)) {
            return request;
        }
        return request.newBuilder().addHeader(headerKey, headerValue).build();
    }
}