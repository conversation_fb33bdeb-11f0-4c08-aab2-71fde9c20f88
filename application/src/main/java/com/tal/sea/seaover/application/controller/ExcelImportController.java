//package com.tal.sea.seaover.application.controller;
//
//import com.tal.sea.seaover.application.service.data.ExcelQuestionImportService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * Excel数据导入控制器
// */
//@Slf4j
//@RestController
//@RequestMapping("/api/question/excel")
//public class ExcelImportController {
//
//    @Autowired
//    private ExcelQuestionImportService excelQuestionImportService;
//
//    /**
//     * 导入gauth_temp.xlsx文件中的题目数据
//     *
//     * @return 导入结果
//     */
//    @GetMapping("/import")
//    public Map<String, Object> importQuestions() {
//        Map<String, Object> result = new HashMap<>();
//
//        try {
//            // Excel文件路径（项目根目录下）
//            String filePath = "/excel/gauth_temp2.xlsx";
//
//            log.info("开始导入Excel文件：{}", filePath);
//
//            int importedCount = excelQuestionImportService.importQuestionsFromExcel(filePath);
//
//            result.put("success", true);
//            result.put("message", "数据导入成功");
//            result.put("importedCount", importedCount);
//
//            log.info("Excel数据导入完成，成功导入{}条数据", importedCount);
//
//        } catch (Exception e) {
//            log.error("Excel数据导入失败", e);
//            result.put("success", false);
//            result.put("message", "数据导入失败：" + e.getMessage());
//            result.put("importedCount", 0);
//        }
//
//        return result;
//    }
//
//    /**
//     * 导入指定路径的Excel文件
//     *
//     * @param filePath Excel文件路径
//     * @return 导入结果
//     */
//    @PostMapping("/import/custom")
//    public Map<String, Object> importQuestionsFromCustomPath(@RequestParam String filePath) {
//        Map<String, Object> result = new HashMap<>();
//
//        try {
//            log.info("开始导入Excel文件：{}", filePath);
//
//            int importedCount = excelQuestionImportService.importQuestionsFromExcel(filePath);
//
//            result.put("success", true);
//            result.put("message", "数据导入成功");
//            result.put("importedCount", importedCount);
//
//            log.info("Excel数据导入完成，成功导入{}条数据", importedCount);
//
//        } catch (Exception e) {
//            log.error("Excel数据导入失败", e);
//            result.put("success", false);
//            result.put("message", "数据导入失败：" + e.getMessage());
//            result.put("importedCount", 0);
//        }
//
//        return result;
//    }
//}
