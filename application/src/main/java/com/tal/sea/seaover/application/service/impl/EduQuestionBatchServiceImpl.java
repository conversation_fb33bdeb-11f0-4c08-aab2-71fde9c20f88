package com.tal.sea.seaover.application.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tal.sea.seaover.application.entity.EduQuestion;
import com.tal.sea.seaover.application.mapper.EduQuestionMapper;
import com.tal.sea.seaover.application.service.EduQuestionBatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * EduQuestion批量操作服务实现类
 */
@Slf4j
@Service
public class EduQuestionBatchServiceImpl extends ServiceImpl<EduQuestionMapper, EduQuestion> implements EduQuestionBatchService {

    private static final int DEFAULT_BATCH_SIZE = 500;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsert(List<EduQuestion> questions) {
        if (questions == null || questions.isEmpty()) {
            log.warn("待插入的EduQuestion列表为空");
            return 0;
        }

        int totalInserted = 0;
        int totalSize = questions.size();

        log.info("开始批量插入EduQuestion数据，总数量：{}，批次大小：{}", totalSize, DEFAULT_BATCH_SIZE);

        // 分批次插入
        for (int i = 0; i < totalSize; i += DEFAULT_BATCH_SIZE) {
            int endIndex = Math.min(i + DEFAULT_BATCH_SIZE, totalSize);
            List<EduQuestion> batch = questions.subList(i, endIndex);
            try {
                // 使用MyBatis-Plus的批量插入
                boolean success = this.saveBatch(batch);
                if (success) {
                    totalInserted += batch.size();
                    log.info("第{}批插入成功，本批数量：{}，累计插入：{}/{}",
                            (i / DEFAULT_BATCH_SIZE + 1), batch.size(), totalInserted, totalSize);
                } else {
                    log.error("第{}批插入失败，本批数量：{}", (i / DEFAULT_BATCH_SIZE + 1), batch.size());
                }
            } catch (Exception e) {
                log.error("第{}批插入异常，本批数量：{}，异常信息：{}",
                        (i / DEFAULT_BATCH_SIZE + 1), batch.size(), e.getMessage(), e);
                // 继续处理下一批，不中断整个流程
            }
        }

        log.info("批量插入完成，成功插入：{}/{}", totalInserted, totalSize);
        return totalInserted;
    }
}
