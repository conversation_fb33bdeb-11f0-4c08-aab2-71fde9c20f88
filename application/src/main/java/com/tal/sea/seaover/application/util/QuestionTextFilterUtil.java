package com.tal.sea.seaover.application.util;

import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 试题文本过滤工具类
 */
public class QuestionTextFilterUtil {
    
    // 匹配<image>标签的正则表达式（包括自闭合标签）
    private static final Pattern IMAGE_TAG_PATTERN = Pattern.compile("<image[^>]*(?:/>|>.*?</image>)", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
    
    // 匹配双$包裹的公式的正则表达式
    private static final Pattern FORMULA_PATTERN = Pattern.compile("\\$\\$([^$]+)\\$\\$");
    
    // 匹配非数字字母的符号
    private static final Pattern SYMBOL_PATTERN = Pattern.compile("[^a-zA-Z0-9\\s]+");
    
    // 匹配多个连续的-号
    private static final Pattern MULTIPLE_DASH_PATTERN = Pattern.compile("-+");
    
    // 匹配多个连续的空格
    private static final Pattern MULTIPLE_SPACE_PATTERN = Pattern.compile("\\s+");

    // 匹配\begin{...}的正则表达式
    private static final Pattern BEGIN_PATTERN = Pattern.compile("\\\\begin\\{align([^}]+)\\}");
    
    // 匹配\end{...}的正则表达式
    private static final Pattern END_PATTERN = Pattern.compile("\\\\end\\{align([^}]+)\\}");
    
    /**
     * 过滤题干文本
     * 
     * @param questionText 原始题干
     * @return 过滤后的题干
     */
    public static String filterQuestionText(String questionText) {
        if (!StringUtils.hasText(questionText)) {
            return questionText;
        }
        
        String result = questionText;
        
        // 1. 去除<image>标签部分
        result = removeImageTags(result);
        
        // 2. 处理公式部分（双$包裹的部分）
        result = processFormulas(result);
        
        // 3. 整体处理：英文大写转小写，符号转换
        result = processOverallText(result);
        
        return result;
    }
    
    /**
     * 去除<image>标签
     * 
     * @param text 文本
     * @return 去除image标签后的文本
     */
    private static String removeImageTags(String text) {
        Matcher matcher = IMAGE_TAG_PATTERN.matcher(text);
        return matcher.replaceAll("");
    }
    
    /**
     * 处理公式部分
     * 
     * @param text 文本
     * @return 处理后的文本
     */
    private static String processFormulas(String text) {
        Matcher matcher = FORMULA_PATTERN.matcher(text);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String formula = matcher.group(1); // 获取$$之间的内容
            String processedFormula = processFormulaContent(formula);
            matcher.appendReplacement(sb, processedFormula);
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
    
    /**
     * 处理公式内容
     * 
     * @param formula 公式内容
     * @return 处理后的公式
     */
    private static String processFormulaContent(String formula) {
        // a. 英文全部大写转小写
        String result = formula.toLowerCase();
        
        // b. 去掉$$，其他任何符号改为-，只保留数字字母
        result = SYMBOL_PATTERN.matcher(result).replaceAll("-");
        
        // c. 多个连续符号只保留一个-
        result = MULTIPLE_DASH_PATTERN.matcher(result).replaceAll("-");
        
        // 去除首尾的-号
        result = result.replaceAll("^-+|-+$", "");
        
        return result;
    }
    
    /**
     * 整体文本处理
     * 
     * @param text 文本
     * @return 处理后的文本
     */
    private static String processOverallText(String text) {
        // a. 英文全部大写转小写
        String result = text.toLowerCase();
        
        // b. 任何符号改为-，只保留数字字母和空格
        result = SYMBOL_PATTERN.matcher(result).replaceAll("-");
        
        // c. 多个连续符号只保留一个-
        result = MULTIPLE_DASH_PATTERN.matcher(result).replaceAll("-");
        
        // d. 多个连续空格合并为一个空格
        result = MULTIPLE_SPACE_PATTERN.matcher(result).replaceAll(" ");
        
        // e. 最后把字符串中的空格替换为-
        result = result.replaceAll("\\s", "-");
        
        // f. 再次处理多个连续的-号，确保只保留一个-
        result = MULTIPLE_DASH_PATTERN.matcher(result).replaceAll("-");
        
        // g. 去除首尾的-号
        result = result.replaceAll("^-+|-+$", "");
        
        return result;
    }
    
    /**
     * 验证问题内容中双$$符号包裹的内容是否包含成对的\begin和\end
     * 
     * @param questionContent 问题内容
     * @return true包含（所有\begin都有对应的\end），false表示不包含
     */
    public static boolean validateBeginEndPairs(String questionContent) {
        if (!StringUtils.hasText(questionContent)) {
            return false;
        }
        
        // 提取所有双$$包裹的内容
        Matcher formulaMatcher = FORMULA_PATTERN.matcher(questionContent);
        
        while (formulaMatcher.find()) {
            String formulaContent = formulaMatcher.group(1); // 获取$$之间的内容
            
            // 验证这段公式内容中的\begin和\end是否成对
            if (validateBeginEndInFormula(formulaContent)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 验证单个公式内容中的\begin和\end是否成对
     * 
     * @param formulaContent 公式内容
     * @return true表示包含成对的\begin和\end，false表示不包含或不成对
     */
    private static boolean validateBeginEndInFormula(String formulaContent) {
        // 使用Map来记录每种环境的\begin和\end的数量
        Map<String, Integer> beginCount = new HashMap<>();
        Map<String, Integer> endCount = new HashMap<>();
        
        // 查找所有\begin{...}
        Matcher beginMatcher = BEGIN_PATTERN.matcher(formulaContent);
        while (beginMatcher.find()) {
            String environment = beginMatcher.group(1);
            beginCount.put(environment, beginCount.getOrDefault(environment, 0) + 1);
        }
        
        // 查找所有\end{...}
        Matcher endMatcher = END_PATTERN.matcher(formulaContent);
        while (endMatcher.find()) {
            String environment = endMatcher.group(1);
            endCount.put(environment, endCount.getOrDefault(environment, 0) + 1);
        }
        
        // 如果没有找到任何\begin，返回false
        if (beginCount.isEmpty()) {
            return false;
        }
        
        // 检查每种环境的\begin和\end数量是否相等
        for (String environment : beginCount.keySet()) {
            int beginNum = beginCount.get(environment);
            int endNum = endCount.getOrDefault(environment, 0);
            if (beginNum != endNum) {
                return false;
            }
        }
        
        // 检查是否有多余的\end（没有对应\begin的\end）
        for (String environment : endCount.keySet()) {
            if (!beginCount.containsKey(environment)) {
                return false;
            }
        }
        
        return true;
    }

}