package com.tal.sea.seaover.application.controller.inner;

import com.tal.sea.seaover.application.dto.BingSeoReportRequest;
import com.tal.sea.seaover.application.dto.RelabelingRequest;
import com.tal.sea.seaover.application.service.EsQuestionService;
import com.tal.sea.seaover.application.service.QuestionSeoReportService;
import com.tal.sea.seaover.application.service.alarm.AlarmService;
import com.tal.sea.seaover.application.service.data.LabelingEduQuestionService;
import com.tal.sea.seaover.application.util.ResponseEntity;
import com.tal.sea.seaover.application.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 数据标注控制器
 * <p>
 * 主要是给试题进行打标(包括试题解析和答案以及年级和知识点)
 * </p>
 */
@Slf4j
@RestController
@RequestMapping("/inner/labeling")
public class InnerLabelingController {
    @Autowired
    private LabelingEduQuestionService labelingEduQuestionService;
    @Autowired
    private AlarmService alarmService;
    @Autowired
    private EsQuestionService esQuestionService;
    @Autowired
    private QuestionSeoReportService questionSeoReportService;

    /**
     * 执行EduQuestion数据标注任务
     *
     * @return 执行结果
     */
    @GetMapping("/questions")
    public ResponseEntity labelingQuestions() {
        long startTime = System.currentTimeMillis();
        try {
            Date date = new Date();
            log.info("开始执行EduQuestion数据标注任务");
            labelingEduQuestionService.labeling();
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            log.info("EduQuestion数据标注任务执行完成，耗时: {}ms", duration);

            log.info("开始向ES添加数据");
            esQuestionService.batchAddQuestionDoc(date);
            log.info("向ES添加数据完成");

            //异步上报SEO URL到必应
            BingSeoReportRequest request = new BingSeoReportRequest();
            request.setStartTime(date);
            questionSeoReportService.asyncReportToBingSeoUrl(request);
            log.info("异步上报SEO URL到必应完成");
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            log.error("EduQuestion数据标注任务执行失败，耗时: {}ms", duration, e);
            alarmService.alarm("EduQuestion数据标注任务执行失败(每日)");
        }
        return ResponseUtil.successWithoutData();
    }

    /**
     * 重新打标指定题目ID集合
     *
     * @param request 重新打标请求参数
     * @return 重新打标结果响应
     */
    @PostMapping("/relabeling")
    public ResponseEntity relabelingQuestions(@Validated @RequestBody RelabelingRequest request) {
        try {
            log.info("接收到重新打标请求，题目ID数量：{}", request.getQuestionIds().size());
            labelingEduQuestionService.relabelingQuestionsComplete(request.getQuestionIds());
            return ResponseUtil.successWithoutData();
        } catch (Exception e) {
            log.error("重新打标接口异常", e);
            return ResponseUtil.failWith500("重新打标失败：" + e.getMessage());
        }
    }

}
