package com.tal.sea.seaover.application.exception;


import com.tal.sea.seaover.application.enums.ErrorEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统服务异常
 * <p>1.调用外部接口异常；</p>
 * <p>2.必须及时处理的异常；</p>
 * <p>3.对外接口参数异常；</p>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BusinessException extends RuntimeException {

    private int code;
    private Object data;

    public BusinessException(int errorCode, String message) {
        super(message);
        this.setCode(errorCode);
    }

    public BusinessException(String message) {
        super(message);
        this.setCode(ErrorEnum.COMMON_CODE.getCode());
    }

    public BusinessException(ErrorEnum errorEnum) {
        super(errorEnum.getMsg());
        this.setCode(errorEnum.getCode());
    }

    public BusinessException(int errorCode, String message, Object data) {
        super(message);
        this.setCode(errorCode);
        this.setData(data);
    }
}
