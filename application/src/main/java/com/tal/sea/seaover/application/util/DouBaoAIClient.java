package com.tal.sea.seaover.application.util;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DouBaoAIClient {
    public static final String URL_OPEN_AI_CHAT_COMPLETION = "http://ai-service-test.tal.com/openai-compatible/v1";
    public static final String DOU_BAO_MODEL = "doubao-pro-32k";

    // 定义 promotion 指令作为系统消息
    private static final String PROMOTION = """
            You are an expert in meticulous format rewriting, skilled in formatting and typesetting mathematical problems. I will provide you with questions, and you need to process them around the [core principles] in accordance with the [standardized processing requirements for questions], and strictly output the data in JSON format as per the output example.
            
            # Core Principles
            - The content of the title itself shall not be altered in any way. No additions or reductions are allowed. Only format replacement processing is permitted.
            
            # Requirements for Standardizing Problem Processing
            1. Identify whether the problem is a single question or multiple questions. If it is multiple questions, store each question in the format of an array element.
            2. Formula/Equation Handling: If you need to use LaTeX formulas in your response, please enclose the formulas with "$$".(for example: $$\\frac{3}{10}$$, $$70 \\div 10$$···)
              - Add "\\t" between formulas to avoid connecting different formulas together.
            3. 将题目拆分成题干(stem)和题目问题(content)。
            4. Answer filling symbol processing:
               - Replace the brackets that require filling in the answers with {answer_brackets}。
               - Replace the underlined parts that require filling in the answers with {answer_underline}。
               - If there were no answer brackets or blanks in the question, you cannot add {answer_brackets} and {answer_underline} by yourself.
            5. 题型判断（不允许出现其他type）：
              - 选择题（type：1）、简答题（type：2）、判断题（type：3）、填空题（type：4）、其他（type：5）
            6.  如果是选择题（非选择题不做处理）：
              - 选择题题干处理：在题干后面加上"\\n"，即选项与题干换行。
              - 选项处理：
                - 如果所有选项（包括空格）总字数超过100，在每个选项后添加“\\n”.
                - 否则不添加“\\n”.
            7. 公式/算式latex格式：将公式/算式中的平方、分数、pi等均渲染成latex格式。
              - 注意：原本的数字2不应该渲染成平方。比如：6 x 82 不能渲染成 6 x 8^{2}
              - 错误示例：x2+y2=5，1/10x1/2，pi
              - 正确示例：x^{2}+y^{2}=5，\\frac{1}{10} \\times \\frac{1}{2}，\\pi
            8. json格式转义：将json中latex的单斜杆"\\"转义成双斜杆"\\\\"。
            
            # 限制
            - 不要输出题目解答的内容。
            - 不能更改题目本身内容，即使它与题目无关。
            
            # JSON格式检查
            - 检查生成的JSON格式是否正确，不正确的JSON需要重新生成。
            
            *** 示例 ***
            - 输入1：
            Solve the system of linear equations 3y - 2x = 11 y = 9 - 2x （）A.(-2,13)  B.(2,2)  C.(2,5)  D.(5,2)
            - 输出1：
            {
                "stem": "Solve the system of linear equations $$3y - 2x = 11$$ \\t  $$y = 9 - 2x$$ {answer_brackets}\\n"
                "content": "A.(-2,13)  B.(2,2)  C.(2,5)  D.(5,2)"
                "type": 1
            }
            
            - 输入2：
            Suppose you have the inequality 2x < 6. Determine the possible values of x. Explain your reasoning.
            Michelle is 3 times as old as her sister Beth. For each question, write and solve an equation or inequality to describe Beth's possible ages. Then, graph the solution set on the number line. a. How old will Beth be when Michelle is at least 27 years old? b. How old will Beth be when Michelle is younger than 30 years old? c. How old will Beth be when Michelle is 42 years old?
            - 输出2:
            [
                {
                    "stem": "Suppose you have the inequality $$2x < 6$$. Determine the possible values of x. Explain your reasoning.",
                    "content": "",
                    "type": 2
                },
                {
                    "stem": "Michelle is 3 times as old as her sister Beth. For each question, write and solve an equation or inequality to describe Beth's possible ages. Then, graph the solution set on the number line.\\n",
                    "content": "a. How old will Beth be when Michelle is at least 27 years old?\\n b. How old will Beth be when Michelle is younger than 30 years old?\\n c. How old will Beth be when Michelle is 42 years old?\\n",
                    "type": 2
                }
            ]
            
            - 输入3：
            what is 5/6 divided by 1/4
            - 输出3：
            {
            	"stem": "What is $$\\\\frac{5}{6}$$ divided by $$\\\\frac{1}{4}$$",
            	"content": "",
            	"type": 2
            }
            """;

    private static final Map<String, ModelDef> dictModels = new HashMap<>();

    static {
        dictModels.put(DOU_BAO_MODEL, new ModelDef("doubao-pro-32k", "300000269:1901f744e05c179756c9bf13af8c7555"));
    }

    static class ModelDef {
        @JsonProperty("model")
        String model;
        @JsonProperty("app_key")
        String appKey;

        public ModelDef(String model, String appKey) {
            this.model = model;
            this.appKey = appKey;
        }
    }

    static class Message {
        @JsonProperty("role")
        String role;
        @JsonProperty("content")
        String content;

        public Message() {
        }

        public Message(String role, String content) {
            this.role = role;
            this.content = content;
        }
    }

    static class Choice {
        @JsonProperty("index")
        int index;
        @JsonProperty("message")
        Message message;
        @JsonProperty("logprobs")
        Map<String, Object> logprobs;
        @JsonProperty("finish_reason")
        String finishReason;

        public Choice() {
            this.message = new Message();
        }
    }

    static class Usage {
        @JsonProperty("prompt_tokens")
        int promptTokens;
        @JsonProperty("completion_tokens")
        int completionTokens;
        @JsonProperty("total_tokens")
        int totalTokens;
    }

    static class ChatCompletionReq {
        @JsonProperty("model")
        String model;
        @JsonProperty("messages")
        List<Message> messages;

        public ChatCompletionReq(String model) {
            this.model = model;
            this.messages = new ArrayList<>();
            // 添加 promotion 作为系统消息
            this.messages.add(new Message("system", PROMOTION));
        }
    }

    static class ChatCompletionResp {
        @JsonProperty("id")
        String id;
        @JsonProperty("object")
        String object;
        @JsonProperty("created")
        long created;
        @JsonProperty("model")
        String model;
        @JsonProperty("choices")
        List<Choice> choices;
        @JsonProperty("usage")
        Usage usage;
        @JsonProperty("system_fingerprint")
        String systemFingerprint;

        public ChatCompletionResp() {
            this.choices = new ArrayList<>();
            this.usage = new Usage();
        }

        public Message getMessage() {
            return choices.isEmpty() ? null : choices.get(0).message;
        }
    }

    public static ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        // 可以在这里配置ObjectMapper，例如设置日期格式、忽略未知属性等 如果不满足可以自行定义
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        return objectMapper;
    }

    public static ChatCompletionResp doChatCompletion(ChatCompletionReq req, int maxRetries) throws Exception {
        if (!dictModels.containsKey(req.model)) {
            throw new IllegalArgumentException("Model " + req.model + " not supported");
        }

        String apiToken = dictModels.get(req.model).appKey;
        ObjectMapper mapper = objectMapper();
        HttpClient client = HttpClient.newBuilder().connectTimeout(Duration.ofSeconds(10)).build();

        Map<String, Object> data = new HashMap<>();
        data.put("model", dictModels.get(req.model).model);
        data.put("messages", req.messages);

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(URL_OPEN_AI_CHAT_COMPLETION + "/chat/completions"))
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + apiToken)
                .POST(HttpRequest.BodyPublishers.ofString(mapper.writeValueAsString(data)))
                .build();

        int retryCount = 0;
        while (retryCount < maxRetries) {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() == 429) {
                retryCount++;
                if (retryCount < maxRetries) {
                    Thread.sleep(1000);
                    continue;
                }
                throw new RuntimeException("达到最大重试次数，API仍然返回配额超限错误");
            }

            if (response.statusCode() != 200) {
                throw new RuntimeException("请求失败，状态码: " + response.statusCode() + ", 响应: " + response.body());
            }

            ChatCompletionResp chatResp = mapper.readValue(response.body(), ChatCompletionResp.class);
            if (chatResp.id == null) {
                throw new IllegalArgumentException("ChatCompletionResp: id not found in response");
            }
            return chatResp;
        }

        throw new RuntimeException("未预期的错误");
    }

    public static String testChatCompletion(String model, String mathProblem) throws Exception {
        ChatCompletionReq req = new ChatCompletionReq(model);
        // 示例数学问题
        req.messages.add(new Message("user", mathProblem));

        ChatCompletionResp resp = doChatCompletion(req, 3);
        return resp.getMessage().content;
    }



    public static void main(String[] args) throws Exception {
        String s = testChatCompletion(DOU_BAO_MODEL, "If $$\\sin { y } =x\\sin { \\left( a+y \\right)  } $$, then $$\\dfrac { dy }{ dx } $$ is\n" +
                "A\n" +
                "$$\\sin { \\left( a+y \\right)  } $$\n" +
                "B\n" +
                "$$\\sin ^{ 2 }{ \\left( a+y \\right)  } $$\n" +
                "C\n" +
                "$$\\dfrac { \\sin ^{ 2 }{ \\left( a+y \\right)  }  }{ \\sin { a }  } $$\n" +
                "D\n" +
                "$$\\dfrac { \\sin { \\left( a+y \\right)  }  }{ \\sin { a }  } $$");
        System.out.println(s);
    }
}