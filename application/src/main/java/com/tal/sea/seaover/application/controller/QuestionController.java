package com.tal.sea.seaover.application.controller;

import com.tal.sea.seaover.application.dto.*;
import com.tal.sea.seaover.application.exception.BusinessException;
import com.tal.sea.seaover.application.service.EduQuestionService;
import com.tal.sea.seaover.application.service.EsQuestionService;
import com.tal.sea.seaover.application.util.ResponseEntity;
import com.tal.sea.seaover.application.util.ResponseUtil;
import com.tal.sea.xpod.tools.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 试题控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/question")
public class QuestionController {

    @Autowired
    private EduQuestionService questionService;

    @Autowired
    private EsQuestionService esQuestionService;

    /**
     * 分页查询试题列表
     *
     * @param request 查询请求参数
     * @return 试题列表响应
     */
    @PostMapping("/list")
    public ResponseEntity<QuestionListResponse> getQuestionList(@Validated @RequestBody QuestionListRequest request) {
        try {
            QuestionListResponse response = questionService.getQuestionList(request);
            return ResponseUtil.successWithData(response);
        } catch (BusinessException e) {
            log.warn("查询试题列表异常", e);
            return ResponseUtil.failWith500("查询试题列表失败：" + e.getMessage());
        }
    }

    /**
     * 查询试题详情
     *
     * @param request 详情查询请求参数
     * @return 试题详情响应
     */
    @PostMapping("/detail")
    public ResponseEntity<QuestionDetailResponse> getQuestionDetail(@Validated @RequestBody QuestionDetailRequest request) {
        try {
            QuestionDetailResponse response = questionService.getQuestionDetail(request);

            return ResponseUtil.successWithData(response);
        } catch (BusinessException e) {
            log.error("查询试题详情异常", e);
            return ResponseUtil.failWith500("查询试题详情失败：" + e.getMessage());
        }
    }

    /**
     * 查询相邻试题
     *
     * @param request 相邻试题查询请求参数
     * @return 相邻试题响应
     */
    @PostMapping("/adjacency")
    public ResponseEntity<QuestionAdjacencyResponse> getAdjacencyQuestion(@Validated @RequestBody QuestionAdjacencyRequest request) {
        try {
            QuestionAdjacencyResponse response = questionService.getAdjacencyQuestion(request);
            return ResponseUtil.successWithData(response);
        } catch (BusinessException e) {
            log.warn("查询学科列表异常", e);
            return ResponseUtil.failWith500("查询相邻试题失败：" + e.getMessage());
        }
    }

    /**
     * 获取学科列表
     *
     * @return 学科列表响应
     */
    @GetMapping("/subjects")
    public ResponseEntity<QuestionSubjectsResponse> getSubjects() {
        try {
            QuestionSubjectsResponse response = questionService.getSubjects();
            return ResponseUtil.successWithData(response);
        } catch (BusinessException e) {
            log.warn("查询学科列表异常", e);
            return ResponseUtil.failWith500("查询学科列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取SEO URL列表
     *
     * @param size 查询数量，默认10000
     * @return SEO URL列表
     */
    @GetMapping("/seoUrls")
    public ResponseEntity<List<String>> listSeoUrls(@RequestParam(defaultValue = "10000") Integer size) {
        try {
            log.info("接收到SEO URL列表查询请求(旧接口)，size: {}", size);
            return ResponseUtil.successWithData(List.of());
        } catch (Exception e) {
            log.error("查询SEO URL列表异常", e);
            return ResponseUtil.failWith500("查询SEO URL列表失败：" + e.getMessage());
        }
    }


    /**
     * 获取SEO URL列表
     *
     * @return SEO URL列表
     */
    @PostMapping("/seoUrls")
    public ResponseEntity<ListSeoUrlResponse> listSeoUrls(@Validated @RequestBody QuestionSeoUrlRequest request) {
        try {
            log.info("接收到SEO URL列表查询请求，参数: {}", GsonUtil.toJson(request));
            // 查询指定数量的题目
            ListSeoUrlResponse questions = questionService.ListSeoUrl(request);
            return ResponseUtil.successWithData(questions);
        } catch (Exception e) {
            log.error("查询SEO URL列表异常", e);
            return ResponseUtil.failWith500("查询SEO URL列表失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除试题
     *
     * @param request 批量删除请求参数
     * @return 删除结果响应
     */
    @PostMapping("/batchDelete")
    public ResponseEntity<QuestionBatchDeleteResponse> batchDeleteQuestions(@Validated @RequestBody QuestionBatchDeleteRequest request) {
        try {
            log.info("接收到批量删除试题请求，题目ID数量：{}", request.getQuestionIds().size());

            // 1. 批量删除数据库中的题目（逻辑删除）
            boolean dbDeleteSuccess = questionService.batchDeleteQuestions(request.getQuestionIds());

            // 2. 批量删除ES中的文档
            boolean esDeleteSuccess = false;
            try {
                esDeleteSuccess = esQuestionService.bulkDeleteDocuments(request.getQuestionIds());
            } catch (Exception e) {
                log.error("删除ES文档失败", e);
                // ES删除失败不影响整体流程，但需要记录日志
            }

            // 3. 构建响应
            return createResponseEntity(request, dbDeleteSuccess, esDeleteSuccess);

        } catch (BusinessException e) {
            log.error("批量删除试题业务异常", e);
            return ResponseUtil.failWith500("批量删除试题失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("批量删除试题系统异常", e);
            return ResponseUtil.failWith500("批量删除试题失败：系统异常");
        }
    }





    private ResponseEntity createResponseEntity(QuestionBatchDeleteRequest request, boolean dbDeleteSuccess, boolean esDeleteSuccess) {
        QuestionBatchDeleteResponse response = new QuestionBatchDeleteResponse();
        response.setDeletedCount(request.getQuestionIds().size());
        response.setDbDeleteSuccess(dbDeleteSuccess);
        response.setEsDeleteSuccess(esDeleteSuccess);

        if (dbDeleteSuccess && esDeleteSuccess) {
            response.setMessage("批量删除成功");
            log.info("批量删除试题成功，数量：{}", request.getQuestionIds().size());
            return ResponseUtil.successWithData(response);
        } else if (dbDeleteSuccess && !esDeleteSuccess) {
            response.setMessage("数据库删除成功，ES删除失败");
            log.warn("批量删除试题部分成功：数据库删除成功，ES删除失败，数量：{}", request.getQuestionIds().size());
            return ResponseUtil.successWithData(response);
        } else {
            response.setMessage("删除失败");
            log.error("批量删除试题失败，数量：{}", request.getQuestionIds().size());
            return ResponseUtil.failWith500("批量删除试题失败");
        }
    }
}