package com.tal.sea.seaover.application.dto.ai;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * OpenAI聊天完成请求DTO
 */
@Data
public class OpenAIChatCompletionRequest {
    
    @JsonProperty("model")
    private String model;
    
    @JsonProperty("messages")
    private List<OpenAIMessage> messages;
    
    public OpenAIChatCompletionRequest(String model) {
        this.model = model;
        this.messages = new ArrayList<>();
    }
    
    public void addMessage(String role, String content) {
        this.messages.add(new OpenAIMessage(role, content));
    }
}