package com.tal.sea.seaover.application.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 召回知识点和年级配置类
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "recall")
public class RecallConfig {
    
    /**
     * 召回接口URL
     */
    private String apiUrl;
}