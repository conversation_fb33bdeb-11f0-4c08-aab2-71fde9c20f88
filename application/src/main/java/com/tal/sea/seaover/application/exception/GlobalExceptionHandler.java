package com.tal.sea.seaover.application.exception;

import com.tal.sea.seaover.application.util.ResponseEntity;
import com.tal.sea.seaover.application.util.ResponseUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    /**
     * servlet异常
     */
    @ExceptionHandler(value = ServletRequestBindingException.class)
    public ResponseEntity ServletRequestBindingExceptionHandler(HttpServletRequest req, ServletRequestBindingException e) {
        log.error("request path: [{}] error:[{}]", req.getServletPath(), e.getMessage(), e);
        return ResponseUtil.failErrorParam(e.getMessage());
    }


    /**
     * 请求参数异常
     */
    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    public ResponseEntity httpMessageNotReadableExceptionHandler(HttpServletRequest req, HttpMessageNotReadableException e) {
        log.error("http param path: [{}] error:[{}]", req.getServletPath(), e.getMessage(), e);
        return ResponseUtil.failErrorParam(e.getMessage());
    }


    /**
     * 参数校验异常处理
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity handleIllegalArgumentException(HttpServletRequest req, MethodArgumentNotValidException e) {
        log.error("IllegalArgumentException path: [{}] error:[{}]", req.getServletPath(), e.getMessage(), e);
        return ResponseUtil.failErrorParam(e.getBindingResult().getFieldError().getDefaultMessage());
    }


    /**
     * 请求方法不支持
     */
    @ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
    public ResponseEntity HttpRequestMethodNotSupportedExceptionHandler(HttpServletRequest req, HttpRequestMethodNotSupportedException e) {
        log.error("request path: [{}] error:[{}]", req.getServletPath(), e.getMessage(), e);
        return ResponseUtil.failErrorParam(e.getMessage());
    }


    /**
     * 兜底异常处理
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity handleException(HttpServletRequest req, Exception e) {
       log.error("Exception path: [{}] error:[{}]", req.getServletPath(), e.getMessage(), e);
        return ResponseUtil.failWith500("system error，please contact the admin");
    }

    @ExceptionHandler(ParamException.class)
    public ResponseEntity handleIllegalArgumentException(HttpServletRequest req, ParamException e){
        log.error("ParamException path: [{}] error:[{}]", req.getServletPath(),e.getMessage());
        return ResponseUtil.failErrorParam(e.getMessage());
    }
}
