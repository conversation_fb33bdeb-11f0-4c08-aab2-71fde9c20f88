package com.tal.sea.seaover.application.dto.ai;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

/**
 * OpenAI选择DTO
 */
@Data
public class OpenAIChoice {
    
    @JsonProperty("index")
    private int index;
    
    @JsonProperty("message")
    private OpenAIMessage message;
    
    @JsonProperty("logprobs")
    private Map<String, Object> logprobs;
    
    @JsonProperty("finish_reason")
    private String finishReason;
    
    public OpenAIChoice() {
        this.message = new OpenAIMessage();
    }
}