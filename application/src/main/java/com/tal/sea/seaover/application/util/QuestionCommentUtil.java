package com.tal.sea.seaover.application.util;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 工具类，用于解析问题评论内容，提取用户名和解决方案内容。
 */
public class QuestionCommentUtil {

    /**
     * 封装用户名和解决方案内容的记录类
     */
    public record QuestionComment(String userName, String comment) {
        public QuestionComment(String userName, String comment) {
            this.userName = userName == null ? "" : userName;
            this.comment = comment == null ? "" : comment;
        }
    }

    /**
     * 解析输入内容，提取 #User Name# 的内容和 #Solution Steps# 后面的所有内容。
     * 保留 Markdown 和 LaTeX 内容不变。
     *
     * @param input 输入的评论内容
     * @return QuestionComment 对象，包含用户名和解决方案内容
     */
    public static QuestionComment parseComment(String input) {
        // 处理空或 null 输入
        if (StringUtils.isEmpty(input)) {
            return new QuestionComment("", "");
        }

        // 按行分割输入
        String[] lines = input.split("\n");
        String userName = "";
        List<String> solutionLines = new ArrayList<>();
        boolean isSolutionSection = false;

        // 正则表达式：匹配 #User Name# 行
        Pattern userNamePattern = Pattern.compile("^#User Name#\\s*(.*)$");

        // 逐行处理
        for (String line : lines) {
            String trimmed = line.trim();

            // 匹配 #User Name#
            Matcher userNameMatcher = userNamePattern.matcher(trimmed);
            if (userNameMatcher.matches()) {
                userName = userNameMatcher.group(1).trim();
                continue;
            }

            // 检测 #Solution Steps# 开始
            if (trimmed.equals("#Solution Steps#")) {
                isSolutionSection = true;
                continue;
            }

            // 收集 #Solution Steps# 后的内容
            if (isSolutionSection) {
                solutionLines.add(line); // 保留原始行，包括 Markdown 和 LaTeX
            }
        }

        // 将解决方案行合并为字符串
        String comment = String.join("\n", solutionLines);

        return new QuestionComment(userName, comment);
    }

    public static void main(String[] args) {
        String str= """
                #User Name# Ellie Chen
                
                #Solution Steps#
                Answer： C. 15
                
                Explain
                This is a question about **equivalent fractions** or **proportions**. The solving step is:
                First, I looked at the fraction 24/40. I thought, "Can I make this fraction simpler?" I know that both 24 and 40 can be divided by 8.
                *   24 divided by 8 is 3.
                *   40 divided by 8 is 5.
                So, 24/40 is the same as 3/5.
                
                Now the problem is 3/5 = 9/a.
                I asked myself, "How do I get from 3 to 9?" I know that 3 times 3 equals 9.
                Since the top number (numerator) was multiplied by 3, the bottom number (denominator) must also be multiplied by 3 to keep the fractions equal!
                *   So, 5 times 3 equals 15.
                That means 'a' must be 15!
                """;
        QuestionComment questionComment = parseComment(str);
        System.out.println(questionComment.userName());
        System.out.println(questionComment.comment());
    }
}
