package com.tal.sea.seaover.application.util;

import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 试题解析和答案处理工具类
 */
public class QuestionSolutionAndAnswerUtil {


    /**
     * 封装 solution 和 answer 内容的类
     */
    public record SolutionAnswer(String solution, String answer) {
        public SolutionAnswer(String solution, String answer) {
            this.solution = solution == null ? "" : solution;
            this.answer = answer == null ? "" : answer;
        }

    }

    /**
     * 解析字符串，分别提取 #solution# 和 #answer# 内容，并封装为 SolutionAnswer 对象
     *
     * @param input 输入字符串，包含 #solution# 和 #answer# 部分
     * @return SolutionAnswer 对象，包含 solution 和 answer 内容
     */
    public static SolutionAnswer parseSolutionAndAnswer(String input) {
        String solution = "";
        String answer = "";

        if (StringUtils.isNotEmpty(input)) {
            // 定义正则表达式，匹配 #solution# 和 #answer# 内容
            Pattern pattern = Pattern.compile("#solution#\\s*(.*?)\\s*(?:#answer#\\s*(.*)|$)", Pattern.DOTALL);
            Matcher matcher = pattern.matcher(input);

            if (matcher.find()) {
                // 提取 solution 内容（组 1）
                solution = matcher.group(1).trim();
                // 提取 answer 内容（组 2，可能为空）
                if (matcher.group(2) != null) {
                    answer = matcher.group(2).trim();
                }
            } else {
                // 如果没有匹配到 #answer#，检查是否只有 #solution#
                if (input.contains("#solution#")) {
                    solution = input.substring(input.indexOf("#solution#") + "#solution#".length()).trim();
                }
            }
        }
        if (StringUtils.isEmpty(solution)) {
            return null;
        }
        String processedSolution = processSolution(solution);
        String processAnswer = "";
        if (StringUtils.isNotEmpty(answer)) {
            processAnswer = processAnswer(answer);
        }
        return new SolutionAnswer(processedSolution, processAnswer);
    }

    /**
     * 处理解析字符串，保留 Markdown 和 LaTeX 内容不变。
     * 判断单题或多题，按 QuestionN.subquestionX 分组，格式化步骤标题，添加分组标题（多题时）。
     *
     * @param input 大模型返回的字符串，可能包含 Markdown 和 LaTeX
     * @return 处理后的字符串
     */
    public static String processSolution(String input) {
        // 处理空或 null 输入
        if (input == null || input.isEmpty()) {
            return input;
        }

        // 按行分割输入
        String[] lines = input.split("\n");
        List<String> output = new ArrayList<>();
        Set<String> uniqueQS = new LinkedHashSet<>(); // 存储 QuestionN.subquestionX，保留顺序
        Map<String, List<String>> groups = new LinkedHashMap<>(); // 按 QS 分组，保留顺序
        String currentQS = null;
        List<String> currentGroup = null;

        // 正则表达式：匹配 QuestionN.subquestionX.stepN(标题)
        Pattern pattern = Pattern.compile("^(Question\\d+\\.subquestion[0-9a-zA-Z]+)\\.step(\\d+)\\((.*)\\)$");

        // 逐行处理
        for (String line : lines) {
            String trimmed = line.trim(); // 去除首尾空格，便于匹配
            Matcher matcher = pattern.matcher(trimmed);
            if (matcher.matches()) {
                // 匹配到步骤标题行
                String qs = matcher.group(1); // QuestionN.subquestionX
                uniqueQS.add(qs);
                // 切换到新分组
                if (!qs.equals(currentQS)) {
                    currentQS = qs;
                    currentGroup = new ArrayList<>();
                    groups.put(qs, currentGroup);
                }
                String stepNum = "step" + matcher.group(2); // stepN
                String title = matcher.group(3).trim(); // 标题内容，去除多余空格
                String processedStep = "**" + stepNum + " " + title + "**";
                if (currentGroup != null) {
                    currentGroup.add(processedStep);
                }
            } else {
                // 非标题行（包括 Markdown、LaTeX、<step> 等），保持原样
                if (currentGroup != null) {
                    currentGroup.add(line); // 添加到当前分组
                } else {
                    output.add(line); // 未分组的行（如前导内容）直接添加到输出
                }
            }
        }

        // 判断单题或多题
        boolean isSingle = uniqueQS.size() == 1;

        // 正则表达式：提取 QuestionN 和 subquestionX
        Pattern qsPattern = Pattern.compile("Question(\\d+)\\.subquestion([0-9a-zA-Z]+)");

        if (isSingle) {
            // 单题：直接输出分组内容，无需标题
            String onlyQS = uniqueQS.iterator().next();
            List<String> group = groups.get(onlyQS);
            if (group != null) {
                output.addAll(group);
            }
        } else {
            // 多题：为每个分组添加标题
            for (String qs : uniqueQS) {
                Matcher qsMatcher = qsPattern.matcher(qs);
                if (qsMatcher.matches()) {
                    String n = qsMatcher.group(1); // Question 编号
                    String sub = qsMatcher.group(2); // subquestion 编号
                    String header = "0".equals(sub) ?
                            "## Question" + n + ":\n" :
                            "## Question" + n + "." + sub + ":\n";
                    output.add(header);
                    List<String> group = groups.get(qs);
                    if (group != null) {
                        output.addAll(group);
                    }
                } else {
                    // 异常：QS 格式不正确，直接添加分组内容
                    List<String> group = groups.get(qs);
                    if (group != null) {
                        output.addAll(group);
                    }
                }
            }
        }

        // 返回处理后的字符串
        return String.join("\n", output);
    }

    /**
     * 处理答案字符串，提取 QuestionN.subquestionX [答案] 并格式化。
     * 单行答案：直接返回答案内容。
     * 多行答案：按 QuestionN.subquestionX 格式化为 **QuestionN:** 或 **QuestionN.X:** 答案。
     *
     * @param input 包含答案的字符串
     * @return 处理后的答案字符串
     */
    public static String processAnswer(String input) {
        // 处理空或 null 输入
        if (input == null || input.isEmpty()) {
            return input;
        }

        // 按行分割输入
        String[] lines = input.split("\n");
        List<String> output = new ArrayList<>();
        List<String> answerLines = new ArrayList<>();
        int answerCount = 0;

        // 正则表达式：匹配 QuestionN.subquestionX [答案] 或 QuestionN.subquestionX 答案
        Pattern pattern = Pattern.compile("^(Question\\d+\\.subquestion[0-9a-zA-Z]+)\\s*(?:\\[(.*?)\\]|(.*))$");

        // 逐行处理，识别答案行
        for (String line : lines) {
            String trimmed = line.trim();
            Matcher matcher = pattern.matcher(trimmed);
            if (matcher.matches()) {
                answerLines.add(trimmed);
                answerCount++;
            } else {
                // 非答案行（如 Markdown、LaTeX），直接保留
                output.add(line);
            }
        }

        // 单行答案处理
        if (answerCount == 1 && answerLines.size() == 1) {
            Matcher matcher = pattern.matcher(answerLines.get(0));
            if (matcher.matches()) {
                String answer = (matcher.group(2) != null ? matcher.group(2) : matcher.group(3)).trim(); // 提取答案内容
                output.add(answer);
            }
        }
        // 多行答案处理
        else if (answerCount > 1) {
            Pattern qsPattern = Pattern.compile("Question(\\d+)\\.subquestion([0-9a-zA-Z]+)");
            for (String answerLine : answerLines) {
                Matcher matcher = pattern.matcher(answerLine);
                if (matcher.matches()) {
                    String qs = matcher.group(1); // QuestionN.subquestionX
                    String answer = (matcher.group(2) != null ? matcher.group(2) : matcher.group(3)).trim(); // 提取答案内容
                    Matcher qsMatcher = qsPattern.matcher(qs);
                    if (qsMatcher.matches()) {
                        String n = qsMatcher.group(1); // Question 编号
                        String sub = qsMatcher.group(2); // subquestion 编号
                        String formattedAnswer = "0".equals(sub) ?
                                "**Question" + n + ":** " + answer :
                                "**Question" + n + "." + sub + ":** " + answer;
                        output.add(formattedAnswer);
                    } else {
                        // 异常：QS 格式不正确，直接添加答案内容
                        output.add(answer);
                    }
                }
            }
        }

        // 返回处理后的字符串
        return String.join("\n", output);
    }

    public static void main(String[] args) {
        String input = """
                #solution#
                Question1.subquestion0.step1(Calculate the Unit Price of a Video Game at Ben's Game World)
                <step>
                <text>To find the unit price of one video game, we need to divide the total cost of the sale pack by the number of games in the pack.</text>
                <formula>$$\\text{Unit Price} = \\frac{\\text{Total Cost of Pack}}{\\text{Number of Games in Pack}}$$</formula>
                <text>Given that 4 video games cost $43.80, we perform the division:</text>
                <formula>$$\\frac{$43.80}{4} = $10.95$$</formula>
                </step>
                
                Question1.subquestionA.step2(Calculate the Cost of 7 Games at Ben's Game World)
                <step>
                <text>Once we know the unit price of one video game, we can find the cost of any number of games by multiplying the unit price by the desired number of games.</text>
                <formula>$$\\text{Total Cost} = \\text{Unit Price} \\times \\text{Number of Games}$$</formula>
                <text>Using the unit price calculated in the previous step, which is $10.95 per game, we calculate the cost of 7 games:</text>
                <formula>$$$10.95 \\times 7 = $76.65$$</formula>
                </step>
                
                Question1.subquestionB.step1(Calculate the Unit Price of a Video Game at Roberto’s Electronics)
                <step>
                <text>The problem states that the unit price at Roberto’s Electronics is 3/5 of the unit price at Ben’s Game World. To find Roberto's unit price, we multiply Ben's unit price by the fraction 3/5.</text>
                <formula>$$\\text{Roberto's Unit Price} = \\frac{3}{5} \\times \\text{Ben's Unit Price}$$</formula>
                <text>From Question A, Ben's unit price is $10.95. We multiply this by 3/5:</text>
                <formula>$$\\frac{3}{5} \\times $10.95 = $6.57$$</formula>
                </step>
                
                Question1.subquestionB.step2(Calculate the Cost of 7 Games at Roberto’s Electronics)
                <step>
                <text>Now that we have Roberto's unit price, we can calculate the total cost of 7 video games by multiplying Roberto's unit price by the number of games.</text>
                <formula>$$\\text{Total Cost} = \\text{Roberto's Unit Price} \\times \\text{Number of Games}$$</formula>
                <text>Using Roberto's unit price of $6.57 per game, we calculate the cost of 7 games:</text>
                <formula>$$$6.57 \\times 7 = $45.99$$</formula>
                </step>
                
                #answer#
                Question1.subquestionA [The unit price is $10.95. 7 games would cost $76.65.]
                Question1.subquestionB [$45.99]
                """;
        SolutionAnswer solutionAnswer = parseSolutionAndAnswer(input);
        String solution = solutionAnswer.solution();

        //System.out.println(solution);
        System.out.println(solutionAnswer.answer());


    }
}
