package com.tal.sea.seaover.application.dto;

import lombok.Data;

/**
 * 批量删除题目响应
 */
@Data
public class QuestionBatchDeleteResponse {

    /**
     * 删除的题目数量
     */
    private Integer deletedCount;

    /**
     * 数据库删除是否成功
     */
    private Boolean dbDeleteSuccess;

    /**
     * ES删除是否成功
     */
    private Boolean esDeleteSuccess;

    /**
     * 删除结果消息
     */
    private String message;

    public QuestionBatchDeleteResponse() {
    }

    public QuestionBatchDeleteResponse(Integer deletedCount, Boolean dbDeleteSuccess, Boolean esDeleteSuccess, String message) {
        this.deletedCount = deletedCount;
        this.dbDeleteSuccess = dbDeleteSuccess;
        this.esDeleteSuccess = esDeleteSuccess;
        this.message = message;
    }
}
