package com.tal.sea.seaover.application.dto.recall;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 召回响应DTO
 */
@Data
public class RecallResponse {
    
    @JsonProperty("code")
    private Integer code;
    
    @JsonProperty("msg")
    private String msg;
    
    @JsonProperty("data")
    private List<RecallDataItem> data;
    
    @JsonProperty("time_cost")
    private Double timeCost;
    
    @JsonProperty("trace_id")
    private String traceId;
    
    @Data
    public static class RecallDataItem {
        @JsonProperty("uuid")
        private String uuid;
        
        @JsonProperty("question_id")
        private String questionId;
        
        @JsonProperty("knowledge_id")
        private String knowledgeId;
        
        @JsonProperty("knowledge_name")
        private String knowledgeName;
        
        @JsonProperty("parent_knowledge_name")
        private String parentKnowledgeName;
        
        @JsonProperty("question_content")
        private String questionContent;
        
        @JsonProperty("question_image")
        private String questionImage;
    }
}