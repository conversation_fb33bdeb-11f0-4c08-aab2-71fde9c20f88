package com.tal.sea.seaover.application.controller.inner;

import com.tal.sea.seaover.application.dto.ExcelImportResult;
import com.tal.sea.seaover.application.service.ExcelQuestionBatchImportService;
import com.tal.sea.seaover.application.service.data.ExcelQuestionImportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/inner/excel/question")
public class InnerImportExcelQuestionController {

    @Autowired
    private ExcelQuestionImportService excelQuestionImportService;

    @Autowired
    private ExcelQuestionBatchImportService excelQuestionBatchImportService;

    /**
     * 导入grth_all.xlsx文件中的题目数据
     *
     * @return 导入结果
     */
    @GetMapping("/import")
    public Map<String, Object> importQuestionsFromGrthExcel() {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("开始导入grth_all.xlsx文件中的题目数据");

            ExcelImportResult result = excelQuestionImportService.importQuestionsFromGrthExcel();

            response.put("success", result.isSuccess());
            response.put("message", result.isSuccess() ? "数据导入完成" : result.getErrorMessage());
            response.put("totalRows", result.getTotalRows());
            response.put("validCount", result.getValidCount());
            response.put("insertedCount", result.getInsertedCount());
            response.put("filteredCount", result.getFilteredCount());
            response.put("failedCount", result.getFailedCount());

            if (result.isSuccess()) {
                log.info("Excel数据导入完成 - 总行数: {}, 有效数据: {}, 成功导入: {}, 过滤重复: {}, 解析失败: {}",
                        result.getTotalRows(), result.getValidCount(), result.getInsertedCount(),
                        result.getFilteredCount(), result.getFailedCount());
            } else {
                log.error("Excel数据导入失败: {}", result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("Excel数据导入异常", e);
            response.put("success", false);
            response.put("message", "数据导入异常：" + e.getMessage());
            response.put("totalRows", 0);
            response.put("validCount", 0);
            response.put("insertedCount", 0);
            response.put("filteredCount", 0);
            response.put("failedCount", 0);
        }

        return response;
    }

    /**
     * 批量导入Excel文件中的题目数据
     * 
     * @param file Excel文件，必须包含source, question_id, question_text, subject四列
     * @return 导入结果
     */
    @PostMapping("/batchImport")
    public Map<String, Object> batchImportQuestionsFromExcel(@RequestParam("file") MultipartFile file) {
        Map<String, Object> response = new HashMap<>
                (Map.of("success", false, "totalRows", 0,
                        "validCount", 0, "insertedCount", 0,
                        "filteredCount", 0, "failedCount", 0));
        try {
            // 检查文件大小 (100MB)
            if (file.getSize() > 100 * 1024 * 1024) {
                response.put("message", "文件大小超过限制，最大支持100MB");
                return response;
            }

            log.info("开始批量导入Excel文件: {} {}({:.2f}MB)",
                    file.getOriginalFilename(), file.getSize() / (1024.0 * 1024.0));

            ExcelImportResult result = excelQuestionBatchImportService.importQuestionsFromExcel(file);
            response.put("success", result.isSuccess());
            response.put("message", result.isSuccess() ? "数据导入完成" : result.getErrorMessage());
            response.put("totalRows", result.getTotalRows());
            response.put("validCount", result.getValidCount());
            response.put("insertedCount", result.getInsertedCount());
            response.put("filteredCount", result.getFilteredCount());
            response.put("failedCount", result.getFailedCount());

            log.info("Excel导入{} - 总行数: {}, 有效: {}, 导入: {}, 过滤: {}, 失败: {}",
                    result.isSuccess() ? "成功" : "失败",
                    result.getTotalRows(), result.getValidCount(), result.getInsertedCount(),
                    result.getFilteredCount(), result.getFailedCount());

        } catch (MaxUploadSizeExceededException e) {
            log.error("文件上传大小超过限制", e);
            response.put("message", "文件大小超过限制，最大支持100MB");
        } catch (Exception e) {
            log.error("Excel导入异常", e);
            response.put("message", "数据导入异常：" + e.getMessage());
        }
        return response;
    }


}
