package com.tal.sea.seaover.application.service.data;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tal.sea.seaover.application.dto.recall.RecallResult;
import com.tal.sea.seaover.application.entity.EduQuestion;
import com.tal.sea.seaover.application.service.EsQuestionService;
import com.tal.sea.seaover.application.enums.YesNoEnum;
import com.tal.sea.seaover.application.mapper.EduQuestionMapper;
import com.tal.sea.seaover.application.service.EduQuestionBatchService;
import com.tal.sea.seaover.application.service.comment.CommentsService;
import com.tal.sea.seaover.application.util.QuestionSolutionAndAnswerUtil;
import com.tal.sea.seaover.application.util.QuestionSolutionProcessor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * EduQuestion数据标注服务
 */
@Slf4j
@Service
@RefreshScope
public class LabelingEduQuestionService {

    @Autowired
    private EduQuestionMapper eduQuestionMapper;

    @Autowired
    private GeminiClientService geminiClientService;

    @Autowired
    private RecallKnowledgeAndGradeService recallKnowledgeAndGradeService;

    @Autowired
    private EduQuestionBatchService eduQuestionBatchService;

    @Autowired
    private EsQuestionService esQuestionService;

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private CommentsService commentsService;

    /**
     * 每次从数据库获取的未标注数据的数量
     */
    private static final int BATCH_SIZE = 100;

    /**
     * 每天跑数据期望打标处理的试题数量
     */
    @Value("${question.labeling.expectationSize:1000}")
    private int expectationSize;

    /**
     * 数据标注处理（并发版本）
     * 分批获取未标注的题目数据，使用并发处理，确保每次处理成功的数量满足要求
     */
    public void labeling() {
        labeling(expectationSize);
    }

    /**
     * 数据标注处理（并发版本，指定成功数量）
     *
     * @param requiredSuccessCount 每次处理必须成功的数量
     */
    public void labeling(int requiredSuccessCount) {
        log.info("开始执行EduQuestion数据标注任务，要求成功处理{}条数据", requiredSuccessCount);
        int totalProcessed = 0;
        int totalSuccess = 0;
        int totalSkipped = 0;
        int currentPage = 1;

        while (totalSuccess < requiredSuccessCount) {
            // 记录开始时间
            long startTime = System.currentTimeMillis();
            // 计算还需要成功处理的数量
            int remainingRequired = requiredSuccessCount - totalSuccess;
            log.info("当前已成功{}条，还需成功{}条", totalSuccess, remainingRequired);

            // 分批获取数据
            Page<EduQuestion> page = getUnlabeledQuestions(currentPage, BATCH_SIZE);
            List<EduQuestion> questions = page.getRecords();

            if (questions.isEmpty()) {
                log.info("没有更多待标注数据，但未达到要求的成功数量。当前成功: {}, 要求: {}",
                        totalSuccess, requiredSuccessCount);
                break;
            }

            log.info("第{}页，获取到{}条待标注数据", currentPage, questions.size());

            // 并发处理当前批次的数据
            ConcurrentProcessResult result = concurrentProcessQuestions(questions);

            // 批量更新数据库
            int batchUpdateSuccess = 0;
            if (!result.successQuestions.isEmpty()) {
                batchUpdateSuccess = batchUpdateQuestions(result.successQuestions);
                log.info("第{}页批量更新完成，成功更新{}条数据", currentPage, batchUpdateSuccess);
            }

            totalProcessed += result.processedCount;
            totalSuccess += batchUpdateSuccess;
            totalSkipped += result.skippedCount;
            long endTime = System.currentTimeMillis();
            double durationSeconds = (endTime - startTime) / 1000.0;
            log.info("第{}页处理完成，当前进度 - 总处理: {}, 成功: {}, 跳过: {}, 用时{}秒",
                    currentPage, totalProcessed, totalSuccess, totalSkipped, durationSeconds);
            // 如果当前页数据少于获取大小，说明数据库中数据不足
            if (questions.size() < BATCH_SIZE && totalSuccess < requiredSuccessCount) {
                log.info("数据库中待标注数据不足，无法满足要求的成功数量。当前成功: {}, 要求: {}",
                        totalSuccess, requiredSuccessCount);
                break;
            }

            currentPage++;
        }

        if (totalSuccess >= requiredSuccessCount) {
            log.info("EduQuestion数据标注任务完成，成功达到要求数量 - 总处理: {}, 成功: {}, 跳过: {}",
                    totalProcessed, totalSuccess, totalSkipped);
        } else {
            log.warn("EduQuestion数据标注任务结束，未达到要求数量 - 总处理: {}, 成功: {}, 跳过: {}, 要求: {}",
                    totalProcessed, totalSuccess, totalSkipped, requiredSuccessCount);
        }
    }

    /**
     * 获取未标注的题目数据
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 分页数据
     */
    private Page<EduQuestion> getUnlabeledQuestions(int pageNum, int pageSize) {
        Page<EduQuestion> page = new Page<>(pageNum, pageSize);

        QueryWrapper<EduQuestion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag", 0)  // 未删除
                .ne("available", 1)  // 不可用（需要标注）
                .orderByAsc("id");   // 按ID升序排列

        return eduQuestionMapper.selectPage(page, queryWrapper);
    }

    /**
     * 处理单个题目的标注
     *
     * @param question 题目对象
     * @return 处理成功的EduQuestion对象，失败返回null
     */
    private EduQuestion processQuestion(EduQuestion question) {
        try {
            String questionText = question.getQuestionText();
            if (StringUtils.isBlank(questionText)) {
                log.warn("题目ID: {} 的questionText为空，跳过处理", question.getId());
                return null;
            }

            // 1. 调用GeminiClientService获取solution
            String solution;
            String answer;
            try {
                String result = geminiClientService.chatCompletion(questionText);
                QuestionSolutionAndAnswerUtil.SolutionAnswer solutionAnswer = QuestionSolutionAndAnswerUtil.parseSolutionAndAnswer(result);
                if (Objects.isNull(solutionAnswer)) {
                    log.info("题目ID:{} 答案和解析处理之后为空，跳过处理", question.getId());
                    return null;
                }
                solution = solutionAnswer.answer();
                answer = solutionAnswer.answer();
            } catch (Exception e) {
                log.error("题目ID: {} 调用Gemini服务失败: {}", question.getId(), e.getMessage(), e);
                return null;
            }

            // 2. 调用RecallKnowledgeAndGradeService获取知识点和年级信息
            RecallResult recallResult;
            try {
                recallResult = recallKnowledgeAndGradeService.recall(questionText);
            } catch (Exception e) {
                log.error("题目ID: {} 调用召回服务失败: {}", question.getId(), e.getMessage(), e);
                return null;
            }

            // 3. 创建更新对象并设置字段
            EduQuestion updateQuestion = new EduQuestion();
            updateQuestion.setId(question.getId());
            updateQuestion.setSolution(solution);
            updateQuestion.setAnswer(answer);
            updateQuestion.setUpdateTime(new Date());


            // 使用BeanUtils复制RecallResult的值到EduQuestion
            if (recallResult != null) {
                BeanUtils.copyProperties(recallResult, updateQuestion);
            }

            // 4. 验证必填字段（parentKnowledgeName可以为空）
            if (!validateRequiredFields(updateQuestion, question.getId())) {
                return null;
            }

            // 5. 设置available=1表示已标注
            boolean validateSolution = QuestionSolutionProcessor.validateSolution(question.getId(), solution);
            if (validateSolution) {
                updateQuestion.setAvailable(1);
            } else {
                updateQuestion.setAvailable(0);
            }
            //6.为试题异步生成三条评论
            if (validateSolution) {
                commentsService.asyncGenerateComments(updateQuestion);
            }
            return updateQuestion;

        } catch (Exception e) {
            log.error("题目ID: {} 处理过程中发生异常: {}", question.getId(), e.getMessage(), e);
            return null;
        }
    }


    /**
     * 验证必填字段（parentKnowledgeName可以为空）
     *
     * @param question   题目对象
     * @param questionId 题目ID（用于日志）
     * @return 是否验证通过
     */
    private boolean validateRequiredFields(EduQuestion question, Long questionId) {
        // solution不能为空
        if (StringUtils.isBlank(question.getSolution())) {
            log.info("题目ID: {} solution为空，跳过处理", questionId);
            return false;
        }

        // grade不能为空
        if (StringUtils.isBlank(question.getGrade())) {
            log.info("题目ID: {} grade为空，跳过处理", questionId);
            return false;
        }

        // knowledgeName不能为空
        if (StringUtils.isBlank(question.getKnowledgeName())) {
            log.info("题目ID: {} knowledgeName为空，跳过处理", questionId);
            return false;
        }

        // knowledgeId不能为空
        if (StringUtils.isBlank(question.getKnowledgeId())) {
            log.info("题目ID: {} knowledgeId为空，跳过处理", questionId);
            return false;
        }
        return true;
    }

    /**
     * 批量更新题目数据（支持分批次更新）
     *
     * @param questions 待更新的题目列表
     * @return 成功更新的数量
     */
    public int batchUpdateQuestions(List<EduQuestion> questions) {
        if (CollectionUtils.isEmpty(questions)) {
            return 0;
        }
        int totalSuccess = 0;
        try {
            boolean success = eduQuestionBatchService.updateBatchById(questions);
            if (success) {
                totalSuccess = questions.size();
                log.info("批量更新完成，成功更新{}条数据", totalSuccess);
                totalSuccess = (int) questions.stream().filter(question ->
                        question.getAvailable() == YesNoEnum.YES.getValue()).count();
            } else {
                log.error("批量更新失败，共{}条数据", questions.size());
            }
        } catch (Exception e) {
            log.error("批量更新过程中发生异常，共{}条数据，异常信息: {}", questions.size(), e.getMessage(), e);
        }

        //获取questions中available=1的数量
        return totalSuccess;
    }

    /**
     * 并发处理题目列表
     *
     * @param questions 待处理的题目列表
     * @return 处理结果
     */
    private ConcurrentProcessResult concurrentProcessQuestions(List<EduQuestion> questions) {
        List<Future<EduQuestion>> futures = new ArrayList<>();
        AtomicInteger processedCount = new AtomicInteger(0);
        AtomicInteger skippedCount = new AtomicInteger(0);
        // 提交所有任务到线程池
        for (EduQuestion question : questions) {
            Future<EduQuestion> future = taskExecutor.submit(() -> {
                try {
                    EduQuestion result = processQuestion(question);
                    processedCount.incrementAndGet();

                    if (result == null) {
                        skippedCount.incrementAndGet();
                    }

                    return result;
                } catch (Exception e) {
                    processedCount.incrementAndGet();
                    skippedCount.incrementAndGet();
                    log.error("题目ID: {} 标注失败，错误信息: {}", question.getId(), e.getMessage(), e);
                    return null;
                }
            });
            futures.add(future);
        }

        // 收集所有结果
        List<EduQuestion> successQuestions = new ArrayList<>();
        for (Future<EduQuestion> future : futures) {
            try {
                EduQuestion result = future.get(300, TimeUnit.SECONDS); // 设置超时时间
                if (result != null) {
                    successQuestions.add(result);
                }
            } catch (TimeoutException e) {
                log.error("处理题目超时", e);
                skippedCount.incrementAndGet();
                future.cancel(true);
            } catch (Exception e) {
                log.error("获取处理结果失败", e);
                skippedCount.incrementAndGet();
            }
        }

        return new ConcurrentProcessResult(successQuestions, processedCount.get(), skippedCount.get());
    }

    /**
     * 重新打标指定题目ID集合
     *
     * @param questionIds 题目ID列表
     * @return 处理结果
     */
    public ConcurrentProcessResult relabelingQuestions(List<Long> questionIds) {
        if (CollectionUtils.isEmpty(questionIds)) {
            log.warn("重新打标：题目ID列表为空");
            return new ConcurrentProcessResult(new ArrayList<>(), 0, 0);
        }
        // 根据ID列表查询题目
        List<EduQuestion> questions = eduQuestionMapper.selectBatchIds(questionIds);
        if (CollectionUtils.isEmpty(questions)) {
            log.warn("重新打标：根据ID列表未查询到任何题目");
            return new ConcurrentProcessResult(new ArrayList<>(), 0, 0);
        }
        log.info("查询到{}条题目数据，开始重新打标", questions.size());
        // 并发处理题目打标
        ConcurrentProcessResult result = concurrentProcessQuestions(questions);
        log.info("重新打标完成，成功：{}，处理总数：{}，跳过：{}",
                result.successQuestions.size(), result.processedCount, result.skippedCount);
        return result;
    }

    /**
     * 重新打标指定题目ID集合（完整流程）
     *
     * @param questionIds 题目ID列表
     * @return 重新打标响应结果
     */
    public void relabelingQuestionsComplete(List<Long> questionIds) {
        log.info("开始重新打标指定题目，数量：{}", questionIds.size());
        try {
            // 1. 重新打标处理
            ConcurrentProcessResult processResult = relabelingQuestions(questionIds);
            // 2. 批量更新数据库
            boolean dbUpdateSuccess = false;
            List<EduQuestion> successQuestions = processResult.successQuestions;
            if (!CollectionUtils.isEmpty(successQuestions)) {
                try {
                    int updateCount = batchUpdateQuestions(successQuestions);
                    dbUpdateSuccess = updateCount > 0;
                    log.info("数据库批量更新完成，成功更新{}条数据", updateCount);
                } catch (Exception e) {
                    log.error("数据库批量更新失败", e);
                }
            }

            // 3. 批量添加到ES
            if (dbUpdateSuccess && !CollectionUtils.isEmpty(successQuestions)) {
                try {
                    esQuestionService.batchAddQuestion(successQuestions);
                    log.info("ES批量更新完成，成功更新{}条数据", successQuestions.size());
                } catch (Exception e) {
                    log.error("ES批量更新失败", e);
                }
            }

        } catch (Exception e) {
            log.error("重新打标过程中发生异常", e);
        }
    }


    /**
     * 并发处理结果内部类
     */
    private static class ConcurrentProcessResult {
        final List<EduQuestion> successQuestions;
        final int processedCount;
        final int skippedCount;

        ConcurrentProcessResult(List<EduQuestion> successQuestions, int processedCount, int skippedCount) {
            this.successQuestions = successQuestions;
            this.processedCount = processedCount;
            this.skippedCount = skippedCount;
        }
    }
}
