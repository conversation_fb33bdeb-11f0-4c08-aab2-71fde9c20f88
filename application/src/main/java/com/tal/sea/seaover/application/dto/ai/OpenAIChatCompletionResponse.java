package com.tal.sea.seaover.application.dto.ai;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * OpenAI聊天完成响应DTO
 */
@Data
public class OpenAIChatCompletionResponse {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("object")
    private String object;
    
    @JsonProperty("created")
    private long created;
    
    @JsonProperty("model")
    private String model;
    
    @JsonProperty("choices")
    private List<OpenAIChoice> choices;
    
    @JsonProperty("usage")
    private OpenAIUsage usage;
    
    @JsonProperty("system_fingerprint")
    private String systemFingerprint;
    
    public OpenAIChatCompletionResponse() {
        this.choices = new ArrayList<>();
        this.usage = new OpenAIUsage();
    }
    
    /**
     * 获取第一个选择的消息
     */
    public OpenAIMessage getMessage() {
        return choices.isEmpty() ? null : choices.get(0).getMessage();
    }
}