package com.tal.sea.seaover.application.config;

import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;

import jakarta.servlet.MultipartConfigElement;

/**
 * 文件上传配置
 */
@Configuration
public class MultipartConfig {

    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        
        // 设置单个文件最大大小为100MB
        factory.setMaxFileSize(DataSize.ofMegabytes(100));
        
        // 设置总上传数据最大大小为200MB
        factory.setMaxRequestSize(DataSize.ofMegabytes(200));
        
        // 设置内存临界值，超过这个值将写入临时文件
        factory.setFileSizeThreshold(DataSize.ofMegabytes(10));
        
        return factory.createMultipartConfig();
    }
}