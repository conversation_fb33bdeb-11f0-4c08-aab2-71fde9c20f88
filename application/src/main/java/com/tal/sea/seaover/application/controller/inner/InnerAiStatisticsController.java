package com.tal.sea.seaover.application.controller.inner;

import com.tal.sea.seaover.application.service.data.AiUsageStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Gemini使用统计内部接口控制器
 * <p>
 *
 * </p>
 */
@Slf4j
@RestController
@RequestMapping("/inner/statistics")
public class InnerAiStatisticsController {

    @Autowired
    private AiUsageStatisticsService usageStatisticsService;
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private ThreadPoolTaskExecutor commentsTaskExecutor;

    /**
     * 获取AI大模型使用token的平均统计信息
     *
     * @return 平均统计信息
     */
    @GetMapping("/average")
    public Map<String, Object> getAverageStatistics() {
        Map<String, Object> response = new HashMap<>();

        try {
            AiUsageStatisticsService.GeminiUsageStatistics stats = usageStatisticsService.getAverageStatistics();
            
            response.put("success", true);
            response.put("callCount", stats.getCallCount());
            response.put("avgPromptTokens", Math.round(stats.getAvgPromptTokens() * 100.0) / 100.0); // 保留2位小数
            response.put("avgCompletionTokens", Math.round(stats.getAvgCompletionTokens() * 100.0) / 100.0);
            response.put("avgTotalTokens", Math.round(stats.getAvgTotalTokens() * 100.0) / 100.0);
            response.put("message", "获取平均统计信息成功");

            log.info("获取Gemini平均统计信息 - 调用次数: {}, 平均prompt tokens: {:.2f}, 平均completion tokens: {:.2f}, 平均total tokens: {:.2f}",
                    stats.getCallCount(), stats.getAvgPromptTokens(), stats.getAvgCompletionTokens(), stats.getAvgTotalTokens());

        } catch (Exception e) {
            log.error("获取Gemini平均统计信息失败", e);
            response.put("success", false);
            response.put("message", "获取统计信息失败：" + e.getMessage());
            response.put("callCount", 0);
            response.put("avgPromptTokens", 0.0);
            response.put("avgCompletionTokens", 0.0);
            response.put("avgTotalTokens", 0.0);
        }

        return response;
    }



    /**
     * 获取完整的统计信息（包含平均值和总计）
     *
     * @return 完整统计信息
     */
    @GetMapping("/complete")
    public Map<String, Object> getCompleteStatistics() {
        Map<String, Object> response = new HashMap<>();

        try {
            AiUsageStatisticsService.GeminiUsageStatistics stats = usageStatisticsService.getAverageStatistics();
            AiUsageStatisticsService.GeminiUsageTotals totals = usageStatisticsService.getTotalStatistics();
            
            response.put("success", true);
            response.put("message", "获取完整统计信息成功");
            
            // 基本信息
            response.put("callCount", stats.getCallCount());
            
            // 平均值信息
            Map<String, Object> averages = new HashMap<>();
            averages.put("promptTokens", Math.round(stats.getAvgPromptTokens() * 100.0) / 100.0);
            averages.put("completionTokens", Math.round(stats.getAvgCompletionTokens() * 100.0) / 100.0);
            averages.put("totalTokens", Math.round(stats.getAvgTotalTokens() * 100.0) / 100.0);
            response.put("averages", averages);
            
            // 总计信息
            Map<String, Object> totalsMap = new HashMap<>();
            totalsMap.put("promptTokens", totals.getTotalPromptTokens());
            totalsMap.put("completionTokens", totals.getTotalCompletionTokens());
            totalsMap.put("totalTokens", totals.getTotalTokens());
            response.put("totals", totalsMap);

            log.info("获取Gemini完整统计信息成功 - 调用次数: {}", stats.getCallCount());

        } catch (Exception e) {
            log.error("获取Gemini完整统计信息失败", e);
            response.put("success", false);
            response.put("message", "获取统计信息失败：" + e.getMessage());
        }

        return response;
    }

    /**
     * 重置统计数据
     *
     * @return 重置结果
     */
    @PostMapping("/reset")
    public Map<String, Object> resetStatistics() {
        Map<String, Object> response = new HashMap<>();

        try {
            usageStatisticsService.resetStatistics();
            
            response.put("success", true);
            response.put("message", "统计数据重置成功");

            log.info("Gemini统计数据重置成功");

        } catch (Exception e) {
            log.error("重置Gemini统计数据失败", e);
            response.put("success", false);
            response.put("message", "重置统计数据失败：" + e.getMessage());
        }

        return response;
    }



    @GetMapping("/monitor/threadpool")
    public String monitorThreadPool() {
        String format = String.format(
                "ActiveCount=%d, PoolSize=%d, QueueSize=%d",
                taskExecutor.getActiveCount(),
                taskExecutor.getPoolSize(),
                taskExecutor.getQueueSize()
        );
        String commentsFormat = String.format(
                "commentsTaskExecutor: ActiveCount=%d, PoolSize=%d, QueueSize=%d",
                commentsTaskExecutor.getActiveCount(),
                commentsTaskExecutor.getPoolSize(),
                commentsTaskExecutor.getQueueSize()
        );
        return format+"  " +commentsFormat;
    }
}
