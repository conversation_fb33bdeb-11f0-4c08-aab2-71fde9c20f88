package com.tal.sea.seaover.application.dto;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 试题seoUrl查询请求参数
 */
@Data
public class QuestionSeoUrlRequest {

    /**
     * 页码
     */
    @NotNull(message = "page cannot be null")
    @Min(value = 1, message = "page  must be greater than 0")
    private Integer page;

    /**
     * 每页数量
     */
    @NotNull(message = "pageSize cannot be null")
    @Min(value = 1, message = "page number must be greater than 0")
    private Integer pageSize = 500;
}