package com.tal.sea.seaover.application.service;

import com.tal.sea.seaover.application.dto.BingSeoReportRequest;

/**
 * 问题SEO URL上报服务接口
 */
public interface QuestionSeoReportService {

    /**
     * 上报问题SEO URL到必应
     *
     * @param request 上报请求参数
     * @return 处理结果消息
     */
    String reportToBingSeoUrl(BingSeoReportRequest request);

    /**
     * 异步上报问题SEO URL到必应
     *
     * @param request
     */
    void asyncReportToBingSeoUrl(BingSeoReportRequest request);
}