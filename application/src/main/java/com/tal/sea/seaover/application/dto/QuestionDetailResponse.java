package com.tal.sea.seaover.application.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 试题详情查询响应
 */
@Data
public class QuestionDetailResponse {
    
    /**
     * 试题ID
     */
    private Long id;
    
    /**
     * 年级
     */
    private String grade;
    
    /**
     * 学科
     */
    private String subject;
    
    /**
     * 题干
     */
    private String questionText;
    
    /**
     * 题干(去除公式)
     */
    private String filterQuestionText;
    
    /**
     * 知识点多个以逗号分隔
     */
    private String knowledge;
    
    /**
     * 题目解析
     */
    private String solution;
    
    /**
     * 答案
     */
    private String answer;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
}