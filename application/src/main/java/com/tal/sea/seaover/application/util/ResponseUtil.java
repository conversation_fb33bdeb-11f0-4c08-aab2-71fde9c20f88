package com.tal.sea.seaover.application.util;

public class ResponseUtil {

    public static <T> ResponseEntity<T> successWithData(T data) {
        String traceId = ThreadMdcUtil.getTraceId();
        Long serverTime = System.currentTimeMillis();
        return new ResponseEntity<>("success", null, null, traceId, serverTime, data);
    }

    public static ResponseEntity successWithoutData() {
        String traceId = ThreadMdcUtil.getTraceId();
        Long serverTime = System.currentTimeMillis();
        return new ResponseEntity<>("success", null, null, traceId, serverTime, null);
    }

    public static ResponseEntity failWith500(String errorMsg) {
        String traceId = ThreadMdcUtil.getTraceId();
        Long serverTime = System.currentTimeMillis();
        return new ResponseEntity<>(null, errorMsg, null, traceId, serverTime, null);
    }

    public static ResponseEntity failErrorParam(String errorMsg) {
        String traceId = ThreadMdcUtil.getTraceId();
        Long serverTime = System.currentTimeMillis();
        return new ResponseEntity<>(null, errorMsg, null, traceId, serverTime, null);
    }

}
