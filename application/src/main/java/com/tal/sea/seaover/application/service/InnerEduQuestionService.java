package com.tal.sea.seaover.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tal.sea.seaover.application.dto.CleanQuestionTextRequest;
import com.tal.sea.seaover.application.entity.EduQuestion;
import com.tal.sea.seaover.application.enums.YesNoEnum;
import com.tal.sea.seaover.application.mapper.EduQuestionMapper;
import com.tal.sea.seaover.application.service.data.DouBaoAiClientService;
import com.tal.sea.seaover.application.util.QuestionTextFilterUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
public class InnerEduQuestionService {
    @Autowired
    private EduQuestionService eduQuestionService;
    @Autowired
    private EsQuestionService esQuestionService;
    @Autowired
    private EduQuestionMapper eduQuestionMapper;
    @Autowired
    private DouBaoAiClientService douBaoAiClientService;
    @Resource
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private EduQuestionBatchService eduQuestionBatchService;

    /**
     * 清理包含成对\begin{}和\end的试题
     */
    public void cleanQuestionsWithBeginEndPairs() {
        log.info("开始清理包含成对\\begin和\\end的试题");
        int pageSize = 5000;
        int currentPage = 1;
        int totalProcessed = 0;
        int totalDeleted = 0;
        while (true) {
            List<EduQuestion> questions = eduQuestionService.listEduAllQuestions(currentPage, pageSize);
            if (questions.isEmpty()) {
                log.info("第{}页没有更多数据，分页查询结束", currentPage);
                break;
            }
            log.info("正在处理第{}页，共{}条试题", currentPage, questions.size());
            // 验证每个试题的questionText
            List<Long> questionsToDeleteIds = new ArrayList<>();
            for (EduQuestion question : questions) {
                totalProcessed++;
                if (question.getQuestionText() != null) {
                    boolean hasBeginEndPairs = QuestionTextFilterUtil.validateBeginEndPairs(question.getQuestionText());
                    if (hasBeginEndPairs) {
                        questionsToDeleteIds.add(question.getId());
                        log.info("试题ID: {} 包含成对的\\begin和\\end，将被删除", question.getId());
                    }
                }
            }

            // 批量删除试题
            if (!questionsToDeleteIds.isEmpty()) {
                log.info("第{}页发现{}个包含成对\\begin和\\end的试题，开始删除", currentPage, questionsToDeleteIds.size());

                // 1. 批量删除数据库中的题目（逻辑删除）
                boolean dbDeleteSuccess = eduQuestionService.batchDeleteQuestions(questionsToDeleteIds);

                // 2. 批量删除ES中的文档
                try {
                    esQuestionService.bulkDeleteDocuments(questionsToDeleteIds);
                    log.info("成功删除ES中的{}个文档", questionsToDeleteIds.size());
                } catch (Exception e) {
                    log.error("删除ES文档失败，试题ID列表: {}", questionsToDeleteIds, e);
                }

                if (dbDeleteSuccess) {
                    totalDeleted += questionsToDeleteIds.size();
                    log.info("成功删除数据库中的{}个试题", questionsToDeleteIds.size());
                } else {
                    log.error("删除数据库中的试题失败，试题ID列表: {}", questionsToDeleteIds);
                }
            }

            currentPage++;

            // 如果当前页的记录数小于页大小，说明已经是最后一页
            if (questions.size() < pageSize) {
                log.info("已处理完所有数据，当前页记录数: {}", questions.size());
                break;
            }
        }

        log.info("清理完成！总共处理了{}个试题，删除了{}个包含成对\\begin和\\end的试题", totalProcessed, totalDeleted);
    }

    /**
     * 清理题目文本
     * 分页查询EduQuestion，使用DouBao AI格式化题目文本，并批量更新到ES
     */
    public String cleanQuestionText(CleanQuestionTextRequest request) {
        log.info("开始执行题目文本清理任务(美化题目的展示)，起始ID: {}", request.getId());

        final int pageSize = 1000;
        Long lastProcessedId = request.getId();
        int batchCount = 0;
        int totalProcessed = 0;
        int totalSuccess = 0;
        int totalFailure = 0;
        AtomicInteger currentBatchSuccess = new AtomicInteger(0);
        AtomicInteger currentBatchFailure = new AtomicInteger(0);

        try {
            while (true) {
                batchCount++;
                currentBatchSuccess.set(0);
                currentBatchFailure.set(0);

                List<EduQuestion> questions = listQuestionByIdScope(lastProcessedId, pageSize);

                if (questions.isEmpty()) {
                    log.info("cleanQuestionText 没有更多数据需要处理，退出循环");
                    break;
                }

                // 使用线程池并发处理当前批次的数据
                List<CompletableFuture<Void>> futures = new ArrayList<>();
                List<EduQuestion> updatedQuestions = new ArrayList<>();

                for (EduQuestion question : questions) {
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        try {
                            // 调用DouBao AI格式化题目文本
                            String formattedContent = douBaoAiClientService.getFormattedQuestionContent(question.getQuestionText());

                            if (StringUtils.isNotEmpty(formattedContent)) {
                                // 更新题目文本
                                question.setQuestionText(formattedContent);
                                synchronized (updatedQuestions) {
                                    updatedQuestions.add(question);
                                }
                                currentBatchSuccess.incrementAndGet();
                            } else {
                                currentBatchFailure.incrementAndGet();
                            }
                        } catch (Exception e) {
                            log.error("处理题目ID: {} 时发生异常", question.getId(), e);
                            currentBatchFailure.incrementAndGet();
                        }
                    }, taskExecutor);

                    futures.add(future);
                }

                // 等待当前批次所有任务完成
                CompletableFuture<Void> allTasks = CompletableFuture.allOf(
                        futures.toArray(new CompletableFuture[0])
                );

                try {
                    allTasks.get(); // 等待所有任务完成
                } catch (Exception e) {
                    log.error("等待批次{}任务完成时发生异常", batchCount, e);
                }
                //批量更新
                eduQuestionBatchService.updateBatchById(updatedQuestions);

                // 更新统计信息
                totalProcessed += questions.size();
                totalSuccess += currentBatchSuccess.get();
                totalFailure += currentBatchFailure.get();

                // 更新lastProcessedId为当前批次的最后一个ID
                lastProcessedId = questions.get(questions.size() - 1).getId();

                log.info("第{}批次处理完成，成功: {}, 失败: {}, 最后处理ID: {}",
                        batchCount, currentBatchSuccess.get(), currentBatchFailure.get(), lastProcessedId);

                // 如果当前批次数据少于页大小，说明已经是最后一批
                if (questions.size() < pageSize) {
                    log.info("已处理完所有数据，退出循环");
                    break;
                }
            }

        } catch (Exception e) {
            log.error("题目文本清理任务执行异常", e);
            return String.format("任务执行异常: %s, 已处理%d条数据, 成功%d条, 失败%d条, 最后处理ID: %s",
                    e.getMessage(), totalProcessed, totalSuccess, totalFailure, lastProcessedId);
        }

        // 记录最后一次的ID值
        log.info("题目文本清理任务完成 - 总共处理: {}条数据, 成功: {}条, 失败: {}条, 最后处理的ID: {}",
                totalProcessed, totalSuccess, totalFailure, lastProcessedId);

        return String.format("任务完成 - 总共处理: %d条数据, 成功: %d条, 失败: %d条, 最后处理的ID: %s",
                totalProcessed, totalSuccess, totalFailure, lastProcessedId);
    }

    private List<EduQuestion> listQuestionByIdScope(Long lastProcessedId, int pageSize) {
        // 使用基于ID的游标分页查询EduQuestion
        LambdaQueryWrapper<EduQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EduQuestion::getDelFlag, YesNoEnum.NO.getValue()); // 未删除的

        // 如果有lastProcessedId，添加ID大于条件（游标分页）
        if (lastProcessedId != null && lastProcessedId > 0) {
            queryWrapper.gt(EduQuestion::getId, lastProcessedId);
        }

        // 按ID升序排列，限制查询数量
        queryWrapper.orderByAsc(EduQuestion::getId);
        queryWrapper.last("LIMIT " + pageSize);
        return eduQuestionMapper.selectList(queryWrapper);
    }


//    /**
//     * 清理题目文本
//     * 分页查询EduQuestion，使用DouBao AI格式化题目文本，并批量更新到ES
//     */
//    public void cleanQuestionTextByIds(List<Long> ids) {
//        // 根据ID列表查询题目
//        List<EduQuestion> questions = eduQuestionMapper.selectBatchIds(ids);
//        List<EduQuestion> updatedQuestions = new ArrayList<>();
//        for (EduQuestion question : questions) {
//            // 调用DouBao AI格式化题目文本
//            String formattedContent = douBaoAiClientService.getFormattedQuestionContent(question.getQuestionText());
//            if (StringUtils.isNotEmpty(formattedContent)) {
//                // 更新题目文本
//                question.setQuestionText(formattedContent);
//                updatedQuestions.add(question);
//            }
//        }
//        //批量更新
//        eduQuestionBatchService.updateBatchById(updatedQuestions);
//    }


}
