package com.tal.sea.seaover.application.service;

import com.tal.sea.seaover.application.entity.EduQuestion;
import com.tal.sea.seaover.application.mapper.EduQuestionMapper;
import com.tal.sea.seaover.application.util.QuestionTextFilterUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class InnerEduQuestionService {
    @Autowired
    private EduQuestionService eduQuestionService;
    @Autowired
    private EsQuestionService esQuestionService;
    @Autowired
    private EduQuestionMapper eduQuestionMapper;

    /**
     * 清理包含成对\begin{}和\end的试题
     */
    public void cleanQuestionsWithBeginEndPairs() {
        log.info("开始清理包含成对\\begin和\\end的试题");
        int pageSize = 5000;
        int currentPage = 1;
        int totalProcessed = 0;
        int totalDeleted = 0;
        while (true) {
            List<EduQuestion> questions = eduQuestionService.listEduAllQuestions(currentPage, pageSize);
            if (questions.isEmpty()) {
                log.info("第{}页没有更多数据，分页查询结束", currentPage);
                break;
            }
            log.info("正在处理第{}页，共{}条试题", currentPage, questions.size());
            // 验证每个试题的questionText
            List<Long> questionsToDeleteIds = new ArrayList<>();
            for (EduQuestion question : questions) {
                totalProcessed++;
                if (question.getQuestionText() != null) {
                    boolean hasBeginEndPairs = QuestionTextFilterUtil.validateBeginEndPairs(question.getQuestionText());
                    if (hasBeginEndPairs) {
                        questionsToDeleteIds.add(question.getId());
                        log.info("试题ID: {} 包含成对的\\begin和\\end，将被删除", question.getId());
                    }
                }
            }

            // 批量删除试题
            if (!questionsToDeleteIds.isEmpty()) {
                log.info("第{}页发现{}个包含成对\\begin和\\end的试题，开始删除", currentPage, questionsToDeleteIds.size());

                // 1. 批量删除数据库中的题目（逻辑删除）
                boolean dbDeleteSuccess = eduQuestionService.batchDeleteQuestions(questionsToDeleteIds);

                // 2. 批量删除ES中的文档
                try {
                    esQuestionService.bulkDeleteDocuments(questionsToDeleteIds);
                    log.info("成功删除ES中的{}个文档", questionsToDeleteIds.size());
                } catch (Exception e) {
                    log.error("删除ES文档失败，试题ID列表: {}", questionsToDeleteIds, e);
                }

                if (dbDeleteSuccess) {
                    totalDeleted += questionsToDeleteIds.size();
                    log.info("成功删除数据库中的{}个试题", questionsToDeleteIds.size());
                } else {
                    log.error("删除数据库中的试题失败，试题ID列表: {}", questionsToDeleteIds);
                }
            }

            currentPage++;

            // 如果当前页的记录数小于页大小，说明已经是最后一页
            if (questions.size() < pageSize) {
                log.info("已处理完所有数据，当前页记录数: {}", questions.size());
                break;
            }
        }

        log.info("清理完成！总共处理了{}个试题，删除了{}个包含成对\\begin和\\end的试题", totalProcessed, totalDeleted);
    }


}
