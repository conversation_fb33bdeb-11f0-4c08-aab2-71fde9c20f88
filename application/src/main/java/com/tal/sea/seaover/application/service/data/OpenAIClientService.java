package com.tal.sea.seaover.application.service.data;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tal.sea.seaover.application.config.OpenAIConfig;
import com.tal.sea.seaover.application.dto.ai.OpenAIChatCompletionRequest;
import com.tal.sea.seaover.application.dto.ai.OpenAIChatCompletionResponse;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class OpenAIClientService {

    @Autowired
    private OpenAIConfig openAIConfig;
    @Autowired
    private ObjectMapper objectMapper;
    private String promotionContent;

    /**
     * 初始化方法，加载提示语文件
     */
    @PostConstruct
    public void init() {
        try {
            ClassPathResource resource = new ClassPathResource(openAIConfig.getPromotionFile());
            try (var inputStream = resource.getInputStream()) {
                this.promotionContent = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
            }
            log.info("成功加载OpenAI提示语文件: {}", openAIConfig.getPromotionFile());
        } catch (IOException e) {
            log.error("加载OpenAI提示语文件失败: {}", openAIConfig.getPromotionFile(), e);
            throw new RuntimeException("Failed to load promotion file", e);
        }
    }

    /**
     * 聊天完成功能
     */
    public String chatCompletion(String mathProblem) throws Exception {
        OpenAIChatCompletionRequest req = createChatRequest();
        req.addMessage("user", mathProblem);
        OpenAIChatCompletionResponse resp = doChatCompletion(req, 3);
        return resp.getMessage() != null ? resp.getMessage().getContent() : "";
    }

    /**
     * 执行聊天完成请求
     */
    public OpenAIChatCompletionResponse doChatCompletion(OpenAIChatCompletionRequest req, int maxRetries) throws Exception {
        HttpClient client = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(120))
                .build();

        Map<String, Object> data = new HashMap<>();
        data.put("model", req.getModel());
        data.put("messages", req.getMessages());

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(openAIConfig.getApiUrl()))
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + openAIConfig.getApiKey())
                .timeout(Duration.ofSeconds(120)) // 设置读取超时（整体请求超时）
                .POST(HttpRequest.BodyPublishers.ofString(objectMapper.writeValueAsString(data)))
                .build();

        int retryCount = 0;
        while (retryCount < maxRetries) {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() == 429) {
                retryCount++;
                if (retryCount < maxRetries) {
                    log.warn("API配额超限，第{}次重试", retryCount);
                    Thread.sleep(1000);
                    continue;
                }
                throw new RuntimeException("达到最大重试次数，API仍然返回配额超限错误");
            }

            if (response.statusCode() != 200) {
                log.error("API请求失败，状态码: {}, 响应: {}", response.statusCode(), response.body());
                throw new RuntimeException("请求失败，状态码: " + response.statusCode() + ", 响应: " + response.body());
            }

            OpenAIChatCompletionResponse chatResp = objectMapper.readValue(response.body(), OpenAIChatCompletionResponse.class);
            if (chatResp.getId() == null) {
                throw new IllegalArgumentException("ChatCompletionResponse: id not found in response");
            }
            return chatResp;
        }

        throw new RuntimeException("未预期的错误");
    }

    /**
     * 创建聊天请求，包含系统提示语
     */
    private OpenAIChatCompletionRequest createChatRequest() {
        OpenAIChatCompletionRequest req = new OpenAIChatCompletionRequest(openAIConfig.getModel());
        req.addMessage("system", promotionContent);
        return req;
    }
}