package com.tal.sea.seaover.application.service.video;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;

@Service
public class VideoAudioProcessorService {

    @Autowired
    private ExcelProcessorService excelProcessorService;

    public byte[] processExcelFile(MultipartFile file) throws Exception {
        // 创建临时目录
        Path tempDir = Files.createTempDirectory("video_audio_process_");
        try {
            return excelProcessorService.processExcel(file.getInputStream(), tempDir);
        } finally {
            // 删除临时目录
            deleteDirectory(tempDir.toFile());
        }
    }

    private void deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }
}