package com.tal.sea.seaover.application.service.data;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tal.sea.seaover.application.config.RecallConfig;
import com.tal.sea.seaover.application.dto.recall.RecallRequest;
import com.tal.sea.seaover.application.dto.recall.RecallResponse;
import com.tal.sea.seaover.application.dto.recall.RecallResult;
import com.tal.sea.seaover.application.util.OkHttpHelperUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 召回知识点和年级服务
 */
@Slf4j
@Service
public class RecallKnowledgeAndGradeService {

    @Autowired
    private RecallConfig recallConfig;
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 固定的base64图片数据
     * 是一个空白的图片
     */
    private static final String BLANK_IMAGE_BASE64 =
            "iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAIAAAAiOj" +
                    "nJAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAEXRFWHRTb2Z0d2FyZQBTbmlwYXN" +
                    "0ZV0Xzt0AAAIUSURBVHic7dLBDcAgEMCw0v13PpYgQkL2BHlkzcwHp/23A3iTsUgYi4SxSBiLhLFIGIuEsUgYi4" +
                    "SxSBiLhLFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxS" +
                    "BiLhLFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxSBiLhL" +
                    "FIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxSBiLh" +
                    "LFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxSBiLhL" +
                    "FIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxS" +
                    "BiLhLFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4Sx" +
                    "SBiLhLFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxSBiLhLFIGIuEsUgYi4SxSBiLhLFIGIvEBiEKBI3Zqo2XAAAAAElFTkSuQmCC";

    /**
     * 召回知识点和年级
     *
     * @param questionContent 题目内容
     * @return 召回结果
     */
    public RecallResult recall(String questionContent) throws Exception {
        // 构建请求对象
        RecallRequest request = buildRecallRequest(questionContent);
        // 发送HTTP请求
        RecallResponse response = sendRecallRequest(request);
        // 处理响应结果
        RecallResult result = processRecallResponse(response);
        return result;
    }

    /**
     * 构建召回请求对象
     */
    private RecallRequest buildRecallRequest(String questionContent) {
        RecallRequest request = new RecallRequest();
        request.setQuestionId("");
        request.setQuestionContent(questionContent);
        request.setQuestionImage(BLANK_IMAGE_BASE64);

        // 构建固定的question_analysis
        List<RecallRequest.QuestionAnalysis> analysisList = new ArrayList<>();
        RecallRequest.QuestionAnalysis analysis = new RecallRequest.QuestionAnalysis();
        analysis.setQuestion("");
        analysis.setStep("1");
        analysis.setTitle("");
        analysis.setDetail("");
        analysisList.add(analysis);

        request.setQuestionAnalysis(analysisList);
        return request;
    }


    private final OkHttpClient client = OkHttpHelperUtil.createClient(6);

    /**
     * 调用IOT服务
     */
    public RecallResponse sendRecallRequest(RecallRequest recallRequest) throws Exception {
        String requestParameter = toJson(recallRequest);
        Request request = OkHttpHelperUtil.buildJsonRequest(recallConfig.getApiUrl(), requestParameter, new HashMap<>());
        Response response = client.newCall(request).execute();
        if (response.code() != 200) {
            log.error("召回接口请求失败，状态码: {}, 响应: {}", response.code(), response.body());
            throw new RuntimeException("召回接口请求失败，状态码: " + response.code());
        }
        if (response.body() == null) {
            log.error("召回接口请求失败，状态码: {}, 响应体为空", response.code());
            throw new RuntimeException("召回接口请求失败，响应体为空");
        }
        return objectMapper.readValue(response.body().string(), RecallResponse.class);

    }


    private String toJson(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("转换json异常, e:{}", e.getMessage(), e);
            return "";
        }
    }


//    /**
//     * 发送召回请求
//     */
//    private RecallResponse sendRecallRequest(RecallRequest request) throws Exception {
//        HttpClient client = HttpClient.newBuilder()
//                .connectTimeout(Duration.ofSeconds(30))
//                .build();
//
//        String requestBody = objectMapper.writeValueAsString(request);
//
//        HttpRequest httpRequest = HttpRequest.newBuilder()
//                .uri(URI.create(recallConfig.getApiUrl()))
//                .header("Content-Type", "application/json")
//                .POST(HttpRequest.BodyPublishers.ofString(requestBody))
//                .build();
//
//        HttpResponse<String> response = client.send(httpRequest, HttpResponse.BodyHandlers.ofString());
//
//        if (response.statusCode() != 200) {
//            log.error("召回接口请求失败，状态码: {}, 响应: {}", response.statusCode(), response.body());
//            throw new RuntimeException("召回接口请求失败，状态码: " + response.statusCode());
//        }
//
//        log.info("响应体: {}", response.body());
//        return objectMapper.readValue(response.body(), RecallResponse.class);
//    }

    /**
     * 处理召回响应结果
     */
    private RecallResult processRecallResponse(RecallResponse response) {
        RecallResult result = new RecallResult();

        if (response.getCode() != 0) {
            log.error("召回接口返回错误，code: {}, msg: {}", response.getCode(), response.getMsg());
            throw new RuntimeException("召回接口返回错误: " + response.getMsg());
        }

        List<RecallResponse.RecallDataItem> dataList = response.getData();
        if (CollectionUtils.isEmpty(dataList)) {
            log.warn("召回接口返回空数据");
            return result;
        }

        // 处理年级和知识点名称
        List<String> grades = new ArrayList<>();
        List<String> knowledgeNames = new ArrayList<>();
        List<String> knowledgeIds = new ArrayList<>();

        for (RecallResponse.RecallDataItem item : dataList) {
            // 处理knowledge_name，以:符号分隔取第一个值作为grade，后面的作为knowledgeName
            String knowledgeName = item.getKnowledgeName();
            if (knowledgeName != null && knowledgeName.contains(":")) {
                String[] parts = knowledgeName.split(":", 2);
                if (parts.length >= 2) {
                    grades.add(parts[0].trim());
                    knowledgeNames.add(parts[1].trim());
                }
            } else if (knowledgeName != null) {
                // 如果没有:分隔符，整个作为knowledgeName
                knowledgeNames.add(knowledgeName.trim());
            }

            // 收集knowledgeId
            if (item.getKnowledgeId() != null) {
                knowledgeIds.add(item.getKnowledgeId());
            }
        }

        // 以逗号拼接结果
        result.setGrade(grades.stream().distinct().collect(Collectors.joining(",")));
        result.setKnowledgeName(knowledgeNames.stream().distinct().collect(Collectors.joining(",")));
        result.setKnowledgeId(knowledgeIds.stream().distinct().collect(Collectors.joining(",")));

        // parentKnowledgeName取第一个
        String parentKnowledgeName = dataList.get(0).getParentKnowledgeName();
        if (StringUtils.isNotEmpty(parentKnowledgeName)) {
            if (parentKnowledgeName.contains(":")) {
                String[] parts = parentKnowledgeName.split(":", 2);
                result.setParentKnowledgeName(parts[1].trim());
            } else {
                result.setParentKnowledgeName(parentKnowledgeName);
            }
        }

        return result;
    }
}