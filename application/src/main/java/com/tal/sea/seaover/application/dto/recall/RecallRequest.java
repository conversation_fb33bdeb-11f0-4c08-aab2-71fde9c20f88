package com.tal.sea.seaover.application.dto.recall;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 召回请求DTO
 */
@Data
public class RecallRequest {
    
    @JsonProperty("question_id")
    private String questionId;
    
    @JsonProperty("question_content")
    private String questionContent;
    
    @JsonProperty("question_image")
    private String questionImage;
    
    @JsonProperty("question_analysis")
    private List<QuestionAnalysis> questionAnalysis;
    
    @Data
    public static class QuestionAnalysis {
        @JsonProperty("question")
        private String question;
        
        @JsonProperty("step")
        private String step;
        
        @JsonProperty("title")
        private String title;
        
        @JsonProperty("detail")
        private String detail;
    }
}