package com.tal.sea.seaover.application.dto;

import lombok.Data;

/**
 * 重新打标响应
 */
@Data
public class RelabelingResponse {

    /**
     * 请求处理的题目总数
     */
    private Integer totalCount;

    /**
     * 成功打标的题目数量
     */
    private Integer successCount;

    /**
     * 跳过的题目数量
     */
    private Integer skippedCount;

    /**
     * 数据库更新是否成功
     */
    private Boolean dbUpdateSuccess;

    /**
     * ES更新是否成功
     */
    private Boolean esUpdateSuccess;

    /**
     * 处理耗时（毫秒）
     */
    private Long duration;

    /**
     * 处理结果消息
     */
    private String message;

    public RelabelingResponse() {
    }

    public RelabelingResponse(Integer totalCount, Integer successCount, Integer skippedCount, 
                             Boolean dbUpdateSuccess, Boolean esUpdateSuccess, Long duration, String message) {
        this.totalCount = totalCount;
        this.successCount = successCount;
        this.skippedCount = skippedCount;
        this.dbUpdateSuccess = dbUpdateSuccess;
        this.esUpdateSuccess = esUpdateSuccess;
        this.duration = duration;
        this.message = message;
    }
}
