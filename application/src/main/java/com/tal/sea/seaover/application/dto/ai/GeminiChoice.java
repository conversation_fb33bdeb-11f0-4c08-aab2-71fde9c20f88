package com.tal.sea.seaover.application.dto.ai;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

/**
 * Gemini选择DTO
 */
@Data
public class GeminiChoice {
    
    @JsonProperty("index")
    private int index;
    
    @JsonProperty("message")
    private GeminiMessage message;
    
    @JsonProperty("logprobs")
    private Map<String, Object> logprobs;
    
    @JsonProperty("finish_reason")
    private String finishReason;
    
    public GeminiChoice() {
        this.message = new GeminiMessage();
    }
}