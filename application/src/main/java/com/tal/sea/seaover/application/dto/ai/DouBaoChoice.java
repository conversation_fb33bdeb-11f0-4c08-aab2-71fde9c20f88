package com.tal.sea.seaover.application.dto.ai;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

/**
 * DouBao选择DTO
 */
@Data
public class DouBaoChoice {
    
    @JsonProperty("index")
    private int index;
    
    @JsonProperty("message")
    private DouBaoMessage message;
    
    @JsonProperty("logprobs")
    private Map<String, Object> logprobs;
    
    @JsonProperty("finish_reason")
    private String finishReason;
    
    public DouBaoChoice() {
        this.message = new DouBaoMessage();
    }
}