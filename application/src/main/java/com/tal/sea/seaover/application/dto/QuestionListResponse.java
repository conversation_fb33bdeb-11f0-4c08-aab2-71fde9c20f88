package com.tal.sea.seaover.application.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 试题列表查询响应
 */
@Data
public class QuestionListResponse {

    /**
     * 数据总数
     */
    private Long total;
    /**
     * 每页数据量
     */
    private Long size;
    /**
     * 当前页码
     */
    private Long pageNum;
    /**
     * 试题列表
     */
    private List<QuestionInfo> questions;

    /**
     * 试题信息
     */
    @Data
    public static class QuestionInfo {
        /**
         * 试题ID
         */
        private Long id;

        /**
         * 年级
         */
        private String grade;

        /**
         * 学科
         */
        private String subject;

        /**
         * 题干
         */
        private String questionText;

        /**
         * 题干(去除公式)
         */
        private String filterQuestionText;

        /**
         * 知识点多个以逗号分隔
         */
        private String knowledge;

        /**
         * 赞数
         */
        private Integer helpful;

        /**
         * 倒赞数
         */
        private Integer notHelpful;

        /**
         * 创建时间
         */
        private Date createTime;

        /**
         * 更新时间
         */
        private Date updateTime;
    }
}