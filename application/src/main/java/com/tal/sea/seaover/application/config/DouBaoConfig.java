package com.tal.sea.seaover.application.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * DouBao AI配置类
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "doubao")
public class DouBaoConfig {
    
    /**
     * DouBao API URL
     *
     */
    private String apiUrl ;
    
    /**
     * DouBao模型名称
     */
    private String model ;
    
    /**
     * API密钥 (app_key)
     */
    private String apiKey;
    
    /**
     * 提示语文件路径
     */
    private String promotionFile;
}