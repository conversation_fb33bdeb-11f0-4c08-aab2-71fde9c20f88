package com.tal.sea.seaover.application.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * DouBao AI配置类
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "doubao")
public class DouBaoConfig {
    
    /**
     * DouBao API URL
     */
    private String apiUrl = "http://ai-service-test.tal.com/openai-compatible/v1/chat/completions";
    
    /**
     * DouBao模型名称
     */
    private String model = "doubao-pro-32k";
    
    /**
     * API密钥 (app_key)
     */
    private String apiKey = "300000269:1901f744e05c179756c9bf13af8c7555";
    
    /**
     * 提示语文件路径
     */
    private String promotionFile = "promotion/doubao-math-promotion.txt";
}