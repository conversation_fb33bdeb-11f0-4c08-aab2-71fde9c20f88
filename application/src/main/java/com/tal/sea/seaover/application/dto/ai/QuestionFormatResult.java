package com.tal.sea.seaover.application.dto.ai;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 题目格式化结果DTO
 */
@Data
public class QuestionFormatResult {

    @JsonProperty("stem")
    private String stem;

    @JsonProperty("content")
    private String content;

    @JsonProperty("type")
    private Integer type;

    /**
     * 获取拼接后的完整题干内容
     */
    public String getFormattedContent() {
        StringBuilder result = new StringBuilder();

        if (!StringUtils.isEmpty(stem)) {
            result.append(stem);
        }

        if (!StringUtils.isEmpty(content)) {
            if (!result.isEmpty()) {
                // 如果stem不为空且不以换行符结尾，添加空格分隔
                if (!stem.endsWith("\n")) {
                    result.append("\n");
                }
            }
            result.append(content);
        }

        return result.toString();
    }
}