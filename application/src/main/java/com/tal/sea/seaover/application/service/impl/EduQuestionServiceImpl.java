package com.tal.sea.seaover.application.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tal.sea.seaover.application.dto.*;
import com.tal.sea.seaover.application.entity.EduQuestion;
import com.tal.sea.seaover.application.enums.YesNoEnum;
import com.tal.sea.seaover.application.exception.BusinessException;
import com.tal.sea.seaover.application.mapper.EduQuestionMapper;
import com.tal.sea.seaover.application.service.EduQuestionService;
import com.tal.sea.seaover.application.util.QuestionTextFilterUtil;
import com.tal.sea.seaover.application.config.SubjectConfig;
import com.tal.sea.seaover.application.util.SeoUrlUtil;
import jakarta.validation.constraints.Max;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 试题服务实现类
 */
@Slf4j
@Service
public class EduQuestionServiceImpl implements EduQuestionService {

    @Autowired
    private EduQuestionMapper eduQuestionMapper;

    @Autowired
    private SubjectConfig subjectConfig;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    public List<EduQuestion> listEduAllQuestions(int currentPage, int pageSize) {
        // 分页查询试题
        Page<EduQuestion> page = new Page<>(currentPage, pageSize);
        LambdaQueryWrapper<EduQuestion> queryWrapper = new LambdaQueryWrapper<>();
        IPage<EduQuestion> questionPage = eduQuestionMapper.selectPage(page, queryWrapper);
        return questionPage.getRecords();
    }

    @Override
    public QuestionListResponse getQuestionList(QuestionListRequest request) {
        log.info("开始查询试题列表，参数：{}", request);

        // 创建分页对象
        Page<EduQuestion> page = new Page<>(request.getPage(), request.getPageSize());

        // 构建查询条件
        QueryWrapper<EduQuestion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("available", YesNoEnum.YES.getValue()); // 只查询可用的数据（经过数据打标）
        // 如果指定了学科，添加学科条件
        if (StringUtils.isNotEmpty(request.getSubject())) {
            queryWrapper.eq("subject", request.getSubject());
        }
        // 按创建时间倒序排列
        queryWrapper.orderByAsc("id");

        // 执行分页查询
        IPage<EduQuestion> questionPage = eduQuestionMapper.selectPage(page, queryWrapper);

        // 构建响应对象
        QuestionListResponse response = new QuestionListResponse();
        response.setTotal(questionPage.getTotal());
        response.setSize(questionPage.getSize());
        response.setPageNum(questionPage.getCurrent());

        List<QuestionListResponse.QuestionInfo> questionInfos = new ArrayList<>();

        for (EduQuestion question : questionPage.getRecords()) {
            QuestionListResponse.QuestionInfo questionInfo = new QuestionListResponse.QuestionInfo();
            BeanUtils.copyProperties(question, questionInfo);
            questionInfo.setKnowledge(question.getKnowledgeName());
            questionInfo.setFilterQuestionText(QuestionTextFilterUtil.filterQuestionText(question.getQuestionText()));
            questionInfos.add(questionInfo);
        }

        response.setQuestions(questionInfos);

        log.info("查询试题列表完成，总数：{}", questionPage.getTotal());
        return response;
    }

    @Override
    public QuestionDetailResponse getQuestionDetail(QuestionDetailRequest request) {
        log.info("开始查询试题详情，参数：{}", request);

        // 根据ID查询试题
        EduQuestion question = eduQuestionMapper.selectById(request.getId());

        if (Objects.isNull(question) || question.getAvailable() == null || question.getAvailable() != YesNoEnum.YES.getValue()) {
            throw new BusinessException("试题不存在、已删除或不可用");
        }
        // 构建响应对象
        QuestionDetailResponse response = new QuestionDetailResponse();
        BeanUtils.copyProperties(question, response);
        response.setKnowledge(question.getKnowledgeName());
        // 处理过滤后的题干
        String filterQuestionText = QuestionTextFilterUtil.filterQuestionText(question.getQuestionText());
        response.setFilterQuestionText(filterQuestionText);

        log.info("查询试题详情完成，试题ID：{}", question.getId());
        return response;
    }

    @Override
    public QuestionAdjacencyResponse getAdjacencyQuestion(QuestionAdjacencyRequest request) {
        log.info("开始查询相邻试题，参数：{}", request);

        // 首先验证当前试题是否存在
        EduQuestion currentQuestion = eduQuestionMapper.selectById(request.getId());
        if (currentQuestion == null || currentQuestion.getDelFlag() == YesNoEnum.YES.getValue() ||
                currentQuestion.getAvailable() == null || currentQuestion.getAvailable() != YesNoEnum.YES.getValue()) {
            throw new BusinessException("试题不存在、已删除或不可用");
        }

        // 查询上一题
        EduQuestion prevQuestion = getPreviousQuestion(Long.valueOf(request.getId()));
        // 查询下一题
        EduQuestion nextQuestion = getNextQuestion(Long.valueOf(request.getId()));

        QuestionAdjacencyResponse response = new QuestionAdjacencyResponse();

        // 设置上一题信息
        if (prevQuestion != null) {
            QuestionAdjacencyResponse.QuestionInfo prevInfo = new QuestionAdjacencyResponse.QuestionInfo();
            prevInfo.setId(String.valueOf(prevQuestion.getId()));
            prevInfo.setQuestionFilterText(QuestionTextFilterUtil.filterQuestionText(prevQuestion.getQuestionText()));
            response.setPrevQuestion(prevInfo);
        }

        // 设置下一题信息
        if (nextQuestion != null) {
            QuestionAdjacencyResponse.QuestionInfo nextInfo = new QuestionAdjacencyResponse.QuestionInfo();
            nextInfo.setId(String.valueOf(nextQuestion.getId()));
            nextInfo.setQuestionFilterText(QuestionTextFilterUtil.filterQuestionText(nextQuestion.getQuestionText()));
            response.setNextQuestion(nextInfo);
        }

        log.info("查询相邻试题完成，当前ID：{}，上一题ID：{}，下一题ID：{}",
                request.getId(),
                prevQuestion != null ? prevQuestion.getId() : "null",
                nextQuestion != null ? nextQuestion.getId() : "null");
        return response;
    }

    /**
     * 获取上一题
     *
     * @param currentId 当前试题ID
     * @return 上一题对象，如果不存在返回null
     */
    private EduQuestion getPreviousQuestion(Long currentId) {
        QueryWrapper<EduQuestion> queryWrapper = new QueryWrapper<>();
        queryWrapper.lt("id", currentId)
                .eq("del_flag", YesNoEnum.NO.getValue())
                .eq("available", YesNoEnum.YES.getValue())
                .orderByDesc("id")
                .last("LIMIT 1");

        return eduQuestionMapper.selectOne(queryWrapper);
    }

    /**
     * 获取下一题
     *
     * @param currentId 当前试题ID
     * @return 下一题对象，如果不存在返回null
     */
    private EduQuestion getNextQuestion(Long currentId) {
        QueryWrapper<EduQuestion> queryWrapper = new QueryWrapper<>();
        queryWrapper.gt("id", currentId)
                .eq("del_flag", YesNoEnum.NO.getValue())
                .eq("available", YesNoEnum.YES.getValue())
                .orderByAsc("id")
                .last("LIMIT 1");

        return eduQuestionMapper.selectOne(queryWrapper);
    }

    @Override
    public QuestionSubjectsResponse getSubjects() {
        log.info("开始获取学科列表");

        QuestionSubjectsResponse response = new QuestionSubjectsResponse();
        List<QuestionSubjectsResponse.SubjectInfo> subjects = new ArrayList<>();

        // 从配置中读取学科列表
        if (subjectConfig.getList() != null) {
            for (SubjectConfig.SubjectItem item : subjectConfig.getList()) {
                QuestionSubjectsResponse.SubjectInfo subjectInfo = new QuestionSubjectsResponse.SubjectInfo();
                subjectInfo.setName(item.getName());
                subjectInfo.setDescription(item.getDescription());
                subjects.add(subjectInfo);
            }
        }

        response.setSubjects(subjects);

        log.info("获取学科列表完成，共{}个学科", subjects.size());
        return response;
    }


    @Override
    public List<EduQuestion> batchQueryQuestions(List<String> questionIds) {
        LambdaQueryWrapper<EduQuestion> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(EduQuestion::getQuestionId, questionIds);
        return eduQuestionMapper.selectList(wrapper);
    }

    @Override
    public boolean batchDeleteQuestions(List<Long> questionIds) {
        if (questionIds == null || questionIds.isEmpty()) {
            log.warn("批量删除题目：题目ID列表为空");
            return true;
        }

        log.info("开始批量删除题目，数量：{}", questionIds.size());

        try {
            // 使用MyBatis-Plus的批量删除（逻辑删除）
            // 由于EduQuestion实体类中的delFlag字段有@TableLogic注解，
            // 调用removeByIds会自动进行逻辑删除，将delFlag设置为1
            int deletedCount = eduQuestionMapper.deleteBatchIds(questionIds);

            log.info("批量删除题目完成，删除数量：{}", deletedCount);
            return true;

        } catch (Exception e) {
            log.error("批量删除题目异常：{}", e.getMessage(), e);
            return false;
        }
    }


    @Override
    public ListSeoUrlResponse ListSeoUrl(QuestionSeoUrlRequest request) {
        // 限制每页最多500条
        int pageSize = Math.min(request.getPageSize(), 500);
        int page = request.getPage();

        log.info("查询SEO URL列表，页码: {}, 每页大小: {}", page, pageSize);

        // 缓存相关常量
        final String CACHE_KEY_PREFIX = "seo_url_cache";
        final String TOTAL_COUNT_KEY = CACHE_KEY_PREFIX + ":total";
        final String PAGE_DATA_KEY_PREFIX = CACHE_KEY_PREFIX + ":page:";
        final long CACHE_EXPIRE_SECONDS = 3600; // 1小时

        try {
            // 1. 检查总数缓存是否存在
            RBucket<Long> totalBucket = redissonClient.getBucket(TOTAL_COUNT_KEY);
            Long cachedTotal = totalBucket.get();

            if (cachedTotal == null) {
                log.info("缓存中不存在总数据，开始从数据库加载并缓存所有分页数据");
                // 缓存不存在，需要从数据库加载所有数据并缓存
                cachedTotal = loadAndCacheAllPages(pageSize, CACHE_KEY_PREFIX, CACHE_EXPIRE_SECONDS);
            }

            if (cachedTotal == 0L) {
                log.info("数据库中没有可用的问题数据");
                return new ListSeoUrlResponse();
            }

            // 2. 从缓存中获取指定页的数据
            String pageKey = PAGE_DATA_KEY_PREFIX + page;
            RBucket<List<ListSeoUrlResponse.SeoUrl>> pageBucket = redissonClient.getBucket(pageKey);
            List<ListSeoUrlResponse.SeoUrl> cachedPageData = pageBucket.get();

            if (cachedPageData == null) {
                log.warn("缓存中不存在第{}页数据，可能页码超出范围", page);
                cachedPageData = new ArrayList<>();
            }

            // 3. 构建响应
            ListSeoUrlResponse response = new ListSeoUrlResponse();
            response.setTotal(cachedTotal);
            response.setSize((long) pageSize);
            response.setPageNum((long) page);
            response.setSeoUrls(cachedPageData);

            log.info("从缓存返回SEO URL列表，总数: {}, 当前页数据量: {}", cachedTotal, cachedPageData.size());
            return response;

        } catch (Exception e) {
            log.error("缓存操作异常，降级到数据库查询", e);
            // 缓存异常时降级到原有逻辑
            return fallbackToDatabase(request, pageSize);
        }
    }

    /**
     * 加载所有数据并缓存到Redis（使用ID游标分页避免深度分页问题）
     */
    private Long loadAndCacheAllPages(int pageSize, String cacheKeyPrefix, long expireSeconds) {
        log.info("开始加载试题并缓存所有SEO URL数据，每页大小: {}", pageSize);
        // 1. 先查询总数
        Long total = getTotal();
        if (total == 0L) {
            // 缓存空结果
            RBucket<Long> totalBucket = redissonClient.getBucket(cacheKeyPrefix + ":total");
            totalBucket.set(0L, expireSeconds, java.util.concurrent.TimeUnit.SECONDS);
            return 0L;
        }
        log.info("总数据量: {}", total);

        // 2. 使用ID游标分页查询并缓存每页数据
        Long lastProcessedId = null; // 游标ID
        int currentPageNum = 1;
        int totalProcessed = 0;

        while (true) {
            try {
                // 构建基于ID的游标查询
                List<EduQuestion> records = listEduQuestions(pageSize, lastProcessedId);
                // 如果没有更多数据，退出循环
                if (records.isEmpty()) {
                    log.info("没有更多数据需要处理，退出循环");
                    break;
                }

                log.info("第{}页，查询到{}条数据，起始ID: {}", currentPageNum, records.size(),
                        lastProcessedId != null ? lastProcessedId : "无");

                // 转换为SEO URL数据
                List<ListSeoUrlResponse.SeoUrl> seoUrls = createResponseSeoUrl(records);

                // 缓存当前页数据
                String pageKey = cacheKeyPrefix + ":page:" + currentPageNum;
                RBucket<List<ListSeoUrlResponse.SeoUrl>> pageBucket = redissonClient.getBucket(pageKey);
                // 设置过期时间比总数据缓存多600秒，确保分页数据在总数据之前过期
                pageBucket.set(seoUrls, expireSeconds + 600, java.util.concurrent.TimeUnit.SECONDS);

                // 更新游标ID为当前批次的最后一个ID
                lastProcessedId = records.get(records.size() - 1).getId();
                totalProcessed += records.size();
                currentPageNum++;

                // 如果当前批次数据少于页大小，说明已经是最后一批
                if (records.size() < pageSize) {
                    log.info("已处理完所有数据，退出循环");
                    break;
                }
            } catch (Exception e) {
                log.error("缓存第{}页数据时发生异常，最后处理ID: {}", currentPageNum, lastProcessedId, e);
                // 继续处理下一页
                currentPageNum++;

                // 如果连续失败太多次，退出循环避免无限循环
                if (currentPageNum > (total / pageSize) + 100) {
                    log.error("连续失败次数过多，强制退出循环");
                    break;
                }
            }
        }

        // 3. 缓存总数（最后设置，确保所有页面数据都已缓存）
        RBucket<Long> totalBucket = redissonClient.getBucket(cacheKeyPrefix + ":total");
        totalBucket.set(total, expireSeconds, java.util.concurrent.TimeUnit.SECONDS);

        log.info("所有SEO URL数据缓存完成，总数: {}, 实际处理: {}, 总页数: {}, 最后处理ID: {}",
                total, totalProcessed, currentPageNum - 1, lastProcessedId);
        return total;
    }


    /**
     * 获取总数
     *
     * @return 总数
     */
    private Long getTotal() {
        LambdaQueryWrapper<EduQuestion> countWrapper = new LambdaQueryWrapper<>();
        countWrapper.eq(EduQuestion::getAvailable, YesNoEnum.YES.getValue());
        return eduQuestionMapper.selectCount(countWrapper);
    }

    /**
     * 基于ID的游标分页查询
     *
     * @param pageSize        每页大小
     * @param lastProcessedId 上一次处理的最后一个ID
     * @return 试题
     */
    private List<EduQuestion> listEduQuestions(int pageSize, Long lastProcessedId) {
        LambdaQueryWrapper<EduQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EduQuestion::getAvailable, YesNoEnum.YES.getValue());
        // 如果有lastProcessedId，添加ID大于条件（游标分页）
        if (lastProcessedId != null) {
            queryWrapper.gt(EduQuestion::getId, lastProcessedId);
        }
        // 按ID升序排列，限制查询数量
        queryWrapper.orderByAsc(EduQuestion::getId);
        queryWrapper.last("LIMIT " + pageSize);

        return eduQuestionMapper.selectList(queryWrapper);
    }

    @NotNull
    private static List<ListSeoUrlResponse.SeoUrl> createResponseSeoUrl(List<EduQuestion> records) {
        return records.stream().map(question -> {
            ListSeoUrlResponse.SeoUrl seoUrl = new ListSeoUrlResponse.SeoUrl();
            seoUrl.setSeoUrl(SeoUrlUtil.generateSeoUrl(question.getSubject(), String.valueOf(question.getId()), question.getQuestionText()));
            seoUrl.setModifyTime(question.getUpdateTime());
            return seoUrl;
        }).collect(Collectors.toList());
    }

    /**
     * 缓存异常时的降级方法
     */
    private ListSeoUrlResponse fallbackToDatabase(QuestionSeoUrlRequest request, int pageSize) {
        log.info("执行数据库降级查询");
        // 使用限制后的pageSize
        QuestionSeoUrlRequest modifiedRequest = new QuestionSeoUrlRequest();
        modifiedRequest.setPage(request.getPage());
        modifiedRequest.setPageSize(pageSize);

        IPage<EduQuestion> questionPage = listQuestionsByPage(modifiedRequest);
        ListSeoUrlResponse response = new ListSeoUrlResponse();
        response.setTotal(questionPage.getTotal());
        response.setSize(questionPage.getSize());
        response.setPageNum(questionPage.getCurrent());

        List<EduQuestion> records = questionPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            response.setSeoUrls(new ArrayList<>());
            return response;
        }

        List<ListSeoUrlResponse.SeoUrl> seoUrls = createResponseSeoUrl(records);

        response.setSeoUrls(seoUrls);
        return response;
    }


    private IPage<EduQuestion> listQuestionsByPage(QuestionSeoUrlRequest request) {
        Page<EduQuestion> page = new Page<>(request.getPage(), request.getPageSize());
        LambdaQueryWrapper<EduQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EduQuestion::getAvailable, YesNoEnum.YES.getValue());
        return eduQuestionMapper.selectPage(page, queryWrapper);
    }

}