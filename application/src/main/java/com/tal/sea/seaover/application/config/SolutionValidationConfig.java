package com.tal.sea.seaover.application.config;

import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * Solution验证配置类
 */
@Data
@Component
public class SolutionValidationConfig {
    
    /**
     * 分页处理的页面大小
     */
    private int pageSize = 1000;
    
    /**
     * 是否启用详细日志
     */
    private boolean enableDetailLog = false;
    
    /**
     * 批量更新的批次大小
     */
    private int batchUpdateSize = 500;
    
    /**
     * 最大处理页数限制（防止无限循环）
     */
    private int maxPageLimit = 10000;
}
