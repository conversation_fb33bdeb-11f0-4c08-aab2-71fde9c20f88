package com.tal.sea.seaover.application.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ListSeoUrlResponse {
    /**
     * 数据总数
     */
    private Long total;

    /**
     * 每页数据量
     */
    private Long size;

    /**
     * 当前页码
     */
    private Long pageNum;

    /**
     * 试题列表
     */
    private List<ListSeoUrlResponse.SeoUrl> seoUrls;

    /**
     * 试题信息
     */
    @Data
    public static class SeoUrl {
        private String seoUrl;
        private Date modifyTime;
    }


}
