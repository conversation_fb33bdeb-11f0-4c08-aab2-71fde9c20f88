package com.tal.sea.seaover.application.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.tal.sea.seaover.application.dto.EduEsQuestionResponse;
import com.tal.sea.seaover.application.dto.SimilarQuestionRequest;
import com.tal.sea.seaover.application.dto.es.EduEsQuestionDto;
import com.tal.sea.seaover.application.entity.EduQuestion;
import com.tal.sea.seaover.application.enums.YesNoEnum;
import com.tal.sea.seaover.application.mapper.EduQuestionMapper;
import com.tal.sea.seaover.application.service.EsQuestionService;
import com.tal.sea.seaover.application.util.QuestionTextFilterUtil;
import com.tal.sea.xpod.tools.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ES题目服务实现类
 */
@Slf4j
@Service
@RefreshScope
public class EsQuestionServiceImpl implements EsQuestionService {
    @Autowired
    private EduQuestionMapper eduQuestionMapper;
    //变成一个配置获取
    @Value("${question.es.index.name}")
    private String questionEsIndexName;


    @Autowired
    private RestHighLevelClient elasticsearchClient;

    private final Gson gson = new GsonBuilder()
            .setDateFormat("yyyy-MM-dd HH:mm:ss") // 指定日期格式
            .create();

    @Override
    public boolean createIndex() throws IOException {
        // 检查索引是否已存在
        if (indexExists()) {
            System.out.println("索引已存在: " + questionEsIndexName);
            return true;
        }

        CreateIndexRequest request = new CreateIndexRequest(questionEsIndexName);

        // 设置索引映射
        String mapping = """
                {
                  "properties": {
                    "id": {
                      "type": "keyword"
                    },
                    "source": {
                      "type": "keyword"
                    },
                    "questionId": {
                      "type": "keyword"
                    },
                    "questionText": {
                      "type": "text",
                      "analyzer": "standard"
                    },
                    "subject": {
                      "type": "keyword"
                    },
                    "answer": {
                      "type": "keyword"
                    },
                    "solution": {
                      "type": "keyword"
                    },
                    "grade": {
                      "type": "keyword"
                    },
                    "knowledgeId": {
                      "type": "keyword"
                    },
                    "knowledgeName": {
                      "type": "keyword"
                    },
                    "parentKnowledgeName": {
                      "type": "keyword"
                    },
                    "helpful": {
                      "type": "integer"
                    },
                    "notHelpful": {
                      "type": "integer"
                    },
                    "createTime": {
                      "type": "date",
                      "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"
                    },
                    "updateTime": {
                      "type": "date",
                      "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"
                    }
                  }
                }
                """;

        request.mapping(mapping, XContentType.JSON);

        // 设置索引设置
        String settings = """
                {
                  "number_of_shards": 5,
                  "number_of_replicas": 1
                }
                """;

        request.settings(settings, XContentType.JSON);

        CreateIndexResponse response = elasticsearchClient.indices().create(request, RequestOptions.DEFAULT);
        System.out.println("创建索引结果: " + response.isAcknowledged());
        return response.isAcknowledged();
    }

    @Override
    public boolean deleteIndex() throws IOException {
        DeleteIndexRequest request = new DeleteIndexRequest(questionEsIndexName);
        AcknowledgedResponse response = elasticsearchClient.indices().delete(request, RequestOptions.DEFAULT);
        return response.isAcknowledged();
    }

    public boolean indexExists() throws IOException {
        GetIndexRequest request = new GetIndexRequest(questionEsIndexName);
        return elasticsearchClient.indices().exists(request, RequestOptions.DEFAULT);
    }

    @Override
    public boolean bulkAddDocuments(List<EduEsQuestionDto> questions) throws IOException {
        BulkRequest request = new BulkRequest();
        for (EduEsQuestionDto question : questions) {
            IndexRequest indexRequest = new IndexRequest(questionEsIndexName);
            if (question.getId() != null) {
                indexRequest.id(question.getId().toString());
            }
            indexRequest.source(gson.toJson(question), XContentType.JSON);
            request.add(indexRequest);
        }

        BulkResponse response = elasticsearchClient.bulk(request, RequestOptions.DEFAULT);
        log.info("批量添加文档结果 -数量:{} ", response.getItems().length);
        return !response.hasFailures();
    }

    @Override
    public List<EduEsQuestionResponse> findSimilarQuestions(SimilarQuestionRequest request) throws IOException {
        Long questionId = request.getQuestionId();

        // 1. 首先查询这个试题ID是否存在
        EduQuestion sourceQuestion = getQuestionById(questionId);
        if (sourceQuestion == null) {
            log.info("题目不存在:{} ", questionId);
            return new ArrayList<>();
        }

        // 2. 获取题目的关键属性
        String subject = sourceQuestion.getSubject();
        String grade = sourceQuestion.getGrade();
        String knowledgeName = sourceQuestion.getKnowledgeName();
        // 3. 执行相似题搜索

        // 优先级1: 相同知识点 + 相同年级
        List<EduEsQuestionResponse> sameKnowledgeAndGrade = searchSimilarQuestions(
                subject, grade, knowledgeName, questionId, true, true);

        List<EduEsQuestionResponse> similarQuestions = new ArrayList<>(sameKnowledgeAndGrade);
        // 如果结果不够，优先级2: 相同知识点 + 任意年级
        if (similarQuestions.size() < request.getSize()) {
            List<EduEsQuestionResponse> sameKnowledgeAnyGrade = searchSimilarQuestions(
                    subject, null, knowledgeName, questionId, true, false);
            // 去重（排除已经添加的题目）
            Set<Long> existingIds = similarQuestions.stream()
                    .map(EduEsQuestionResponse::getId)
                    .collect(Collectors.toSet());
            sameKnowledgeAnyGrade.stream()
                    .filter(q -> !existingIds.contains(q.getId()))
                    .forEach(similarQuestions::add);

        }

        // 如果结果再不够，优先级3：同年级就行
        if (similarQuestions.size() < request.getSize()) {
            List<EduEsQuestionResponse> sameGradeOnly = searchSimilarQuestions(
                    subject, grade, null, questionId, false, true);
            // 去重（排除已经添加的题目）
            Set<Long> existingIds = similarQuestions.stream()
                    .map(EduEsQuestionResponse::getId)
                    .collect(Collectors.toSet());

            sameGradeOnly.stream()
                    .filter(q -> !existingIds.contains(q.getId()))
                    .forEach(similarQuestions::add);
        }
        // 限制返回数量
        return similarQuestions.stream()
                .limit(request.getSize())
                .peek(e -> e.setFilterQuestionText(QuestionTextFilterUtil.filterQuestionText(e.getQuestionText())))
                .collect(Collectors.toList());
    }

    /**
     * 根据questionId获取题目信息
     */
    private EduQuestion getQuestionById(Long questionId) {
        EduQuestion question = eduQuestionMapper.selectById(questionId);
        // 检查题目是否可用
        if (question != null && (question.getAvailable() == null || question.getAvailable() != 1)) {
            return null; // 不可用的题目视为不存在
        }
        return question;
    }


    /**
     * 查询es获取相似题
     *
     * @param subject           学科
     * @param grade             年级
     * @param knowledgeName     知识点名称
     * @param excludeQuestionId 排除的题ID
     * @param strictKnowledge   是否严格知识点
     * @param strictGrade       是否严格年级
     * @return 相似题目列表
     * @throws IOException ES操作异常
     */
    private List<EduEsQuestionResponse> searchSimilarQuestions(
            String subject, String grade,
            String knowledgeName, Long excludeQuestionId,
            boolean strictKnowledge, boolean strictGrade) throws IOException {

        SearchRequest searchRequest = new SearchRequest(questionEsIndexName);
        SearchSourceBuilder builder = new SearchSourceBuilder();

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        // 1. subject 精准匹配（必须条件）
        if (StringUtils.isNotEmpty(subject)) {
            boolQuery.must(QueryBuilders.termQuery("subject", subject));
        }

        // 2. grade 精准匹配（可选条件）
        if (strictGrade && StringUtils.isNotEmpty(grade)) {
            List<String> grades = parseCommaSeparatedValues(grade);
            if (grades.size() == 1) {
                boolQuery.must(QueryBuilders.termQuery("grade", grades.get(0)));
            } else {
                BoolQueryBuilder gradeQuery = QueryBuilders.boolQuery();
                for (String g : grades) {
                    gradeQuery.should(QueryBuilders.termQuery("grade", g));
                }
                boolQuery.must(gradeQuery);
            }
        }

        // 3. knowledgeName 匹配
        if (strictKnowledge && StringUtils.isNotEmpty(knowledgeName)) {
            List<String> knowledgeNames = parseCommaSeparatedValues(knowledgeName);
            if (knowledgeNames.size() == 1) {
                boolQuery.should(QueryBuilders.termQuery("knowledgeName", knowledgeNames.get(0)));
            } else {
                BoolQueryBuilder knowledgeQuery = QueryBuilders.boolQuery();
                for (String kn : knowledgeNames) {
                    knowledgeQuery.should(QueryBuilders.termQuery("knowledgeName", kn));
                }
                boolQuery.should(knowledgeQuery);
            }
        }

        // 5. 排除当前题目
        if (!Objects.isNull(excludeQuestionId)) {
            boolQuery.mustNot(QueryBuilders.termQuery("id", excludeQuestionId));
        }

        builder.query(boolQuery);
        builder.size(50); // 获取更多结果用于排序

        searchRequest.source(builder);

        SearchResponse response = elasticsearchClient.search(searchRequest, RequestOptions.DEFAULT);

        List<EduEsQuestionResponse> results = new ArrayList<>();
        String matchReason = generateMatchReason(strictKnowledge, strictGrade);

        for (SearchHit hit : response.getHits().getHits()) {
            EduEsQuestionResponse dto = gson.fromJson(hit.getSourceAsString(), EduEsQuestionResponse.class);
            dto.setScore(hit.getScore());
            dto.setMatchReason(matchReason);
            results.add(dto);
        }
        return results;
    }

    /**
     * 解析逗号分隔的值
     */
    private List<String> parseCommaSeparatedValues(String value) {
        if (value == null || value.trim().isEmpty()) {
            return new ArrayList<>();
        }

        return Arrays.stream(value.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }

    /**
     * 生成匹配原因描述
     */
    private String generateMatchReason(boolean strictKnowledge, boolean strictGrade) {
        if (strictKnowledge && strictGrade) {
            return "相同知识点+相同年级";
        } else if (strictKnowledge) {
            return "相同知识点+任意年级";
        } else if (strictGrade) {
            return "相同年级";
        } else {
            return "相同学科";
        }
    }


    /**
     * 添加doc到index中
     *
     * @param date 日期作为查询questiond的条件大于这个时间才添加 为空默认添加全部
     */
    @Override
    public void batchAddQuestionDocByData(Date date) {
        LambdaQueryWrapper<EduQuestion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EduQuestion::getDelFlag, 0);
        wrapper.eq(EduQuestion::getAvailable, 1);
        if (!Objects.isNull(date)) {
            //更新时间大于当前时间
            wrapper.gt(EduQuestion::getUpdateTime, date);
        }
        List<EduQuestion> questions = eduQuestionMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(questions)) {
            log.info("batchAddQuestionDoc 没有需要添加的题目");
            return;
        }
        List<EduEsQuestionDto> questionsEs = questions.stream()
                .map(this::createEduEsQuestionDto).toList();
        try {
            bulkAddDocuments(questionsEs);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }


    private EduEsQuestionDto createEduEsQuestionDto(EduQuestion question) {
        EduEsQuestionDto dto = new EduEsQuestionDto();
        dto.setId(question.getId());
        dto.setSource(question.getSource());
        dto.setQuestionId(question.getQuestionId());
        dto.setQuestionText(question.getQuestionText());
        dto.setSubject(question.getSubject());
        dto.setAnswer(question.getAnswer());
        String grade = question.getGrade();
        if (StringUtils.isNotEmpty(grade)) {
            String[] grades = grade.split(",");
            dto.setGrade(Arrays.asList(grades));
        }
        if (StringUtils.isNotEmpty(question.getKnowledgeName())) {
            String[] knowledgeNames = question.getKnowledgeName().split(",");
            dto.setKnowledgeName(Arrays.asList(knowledgeNames));
        }
        if (StringUtils.isNotEmpty(question.getKnowledgeId())) {
            String[] knowledgeIds = question.getKnowledgeId().split(",");
            dto.setKnowledgeId(Arrays.asList(knowledgeIds));
        }
        dto.setParentKnowledgeName(question.getParentKnowledgeName());
        return dto;
    }

    @Override
    public boolean bulkDeleteDocuments(List<Long> questionIds) throws IOException {
        if (questionIds == null || questionIds.isEmpty()) {
            log.warn("批量删除ES文档：题目ID列表为空");
            return true;
        }
        log.info("开始批量删除ES文档，数量：{}", questionIds.size());

        BulkRequest bulkRequest = new BulkRequest();
        // 为每个题目ID创建删除请求
        for (Long questionId : questionIds) {
            DeleteRequest deleteRequest = new DeleteRequest(questionEsIndexName);
            deleteRequest.id(questionId.toString());
            bulkRequest.add(deleteRequest);
        }
        try {
            BulkResponse bulkResponse = elasticsearchClient.bulk(bulkRequest, RequestOptions.DEFAULT);
            if (bulkResponse.hasFailures()) {
                log.error("批量删除ES文档部分失败：{}", bulkResponse.buildFailureMessage());
                return false;
            } else {
                log.info("批量删除ES文档成功，删除数量：{}", questionIds.size());
                return true;
            }
        } catch (IOException e) {
            log.error("批量删除ES文档异常：{}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public boolean batchAddQuestion(List<EduQuestion> questions) {
        if (CollectionUtils.isEmpty(questions)) {
            log.warn("批量添加ES文档：题目ID列表为空");
            return true;
        }
        try {
            // 转换为ES文档格式(只更新可用的题目)
            List<EduEsQuestionDto> questionsEs = questions.stream()
                    .filter(question -> question.getAvailable() == YesNoEnum.YES.getValue()).
                    map(this::createEduEsQuestionDto).toList();
            log.info("开始根据ID列表批量添加ES文档，数量：{}", questions.size());
            // 批量添加到ES
            boolean success = bulkAddDocuments(questionsEs);
            if (success) {
                log.info("根据ID列表批量添加ES文档成功，实际添加数量：{}", questionsEs.size());
            } else {
                log.error("根据ID列表批量添加ES文档失败，数量：{}", questionsEs.size());
            }
            return success;

        } catch (Exception e) {
            log.error("根据ID列表批量添加ES文档异常：{}", e.getMessage(), e);
        }
        return false;
    }
}