package com.tal.sea.seaover.application.dto.ai;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * DouBao聊天完成请求DTO
 */
@Data
public class DouBaoChatCompletionRequest {
    
    @JsonProperty("model")
    private String model;
    
    @JsonProperty("messages")
    private List<DouBaoMessage> messages;
    
    public DouBaoChatCompletionRequest(String model) {
        this.model = model;
        this.messages = new ArrayList<>();
    }
    
    public void addMessage(String role, String content) {
        this.messages.add(new DouBaoMessage(role, content));
    }
}