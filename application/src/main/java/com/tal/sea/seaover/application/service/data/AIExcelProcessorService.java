package com.tal.sea.seaover.application.service.data;

import com.tal.sea.seaover.application.util.QuestionCommentUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * OpenAI Excel处理服务
 * 读取Excel中的question_text列，调用OpenAI API处理，并将结果写入最后一列
 */
@Slf4j
@Service
public class AIExcelProcessorService {

    @Autowired
    private OpenAIClientService openAIClientService;
    @Autowired
    private DouBaoAiClientService douBaoAiClientService;
    @Autowired
    private GeminiClientService geminiClientService;

    private static final int THREAD_POOL_SIZE = 5; // 限制并发数避免API限流

    /**
     * 处理Excel文件
     *
     * @param excelInputStream Excel文件输入流
     * @return 处理后的Excel文件字节数组
     * @throws Exception 处理异常
     */
    public byte[] processExcel(InputStream excelInputStream) throws Exception {
        Workbook workbook = new XSSFWorkbook(excelInputStream);
        try {
            log.info("开始处理Excel文件，共{}个工作表", workbook.getNumberOfSheets());

            // 添加结果列头
            addResultColumnHeader(workbook);

            // 处理每个sheet
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                log.info("开始处理第{}个工作表: {}", i + 1, sheet.getSheetName());
                processSheet(sheet);
            }

            // 将处理后的Excel写入字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            log.info("Excel文件处理完成");
            return outputStream.toByteArray();
        } finally {
            workbook.close();
        }
    }

    /**
     * 添加结果列头
     */
    private void addResultColumnHeader(Workbook workbook) {
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheet = workbook.getSheetAt(i);
            if (sheet.getPhysicalNumberOfRows() > 0) {
                Row headerRow = sheet.getRow(0);
                if (headerRow != null) {
                    int lastCellNum = headerRow.getLastCellNum();
                    Cell resultHeaderCell = headerRow.createCell(lastCellNum);
                    resultHeaderCell.setCellValue("result");
                    log.info("在工作表'{}'中添加结果列头", sheet.getSheetName());
                }
            }
        }
    }

    /**
     * 处理单个工作表
     */
    private void processSheet(Sheet sheet) throws Exception {
        // 查找question_text列
        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            log.warn("工作表'{}'没有标题行，跳过处理", sheet.getSheetName());
            return;
        }

        int questionTextColumnIndex = findColumnIndex(headerRow, "question_text");
        if (questionTextColumnIndex == -1) {
            log.warn("工作表'{}'中未找到question_text列，跳过处理", sheet.getSheetName());
            return;
        }

        log.info("在工作表'{}'中找到question_text列，索引: {}", sheet.getSheetName(), questionTextColumnIndex);

        // 创建线程池进行并发处理
        ExecutorService executor = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
        List<Future<?>> futures = new ArrayList<>();

        try {
            // 处理每一行数据
            int totalRows = sheet.getLastRowNum();
            log.info("工作表'{}'共有{}行数据需要处理", sheet.getSheetName(), totalRows);

            for (int i = 1; i <= totalRows; i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                final int rowIndex = i;
                final int finalQuestionTextColumnIndex = questionTextColumnIndex;

                // 提交任务到线程池
                futures.add(executor.submit(() -> {
                    try {
                        processRow(sheet, rowIndex, finalQuestionTextColumnIndex);
                    } catch (Exception e) {
                        log.error("处理第{}行时发生异常: {}", rowIndex + 1, e.getMessage(), e);
                    }
                }));
            }

            // 等待所有任务完成
            for (Future<?> future : futures) {
                future.get(10, TimeUnit.MINUTES); // 设置超时时间
            }

            log.info("工作表'{}'处理完成", sheet.getSheetName());
        } finally {
            executor.shutdown();
            executor.awaitTermination(15, TimeUnit.MINUTES);
        }
    }

    /**
     * 处理单行数据
     */
    private void processRow(Sheet sheet, int rowIndex, int questionTextColumnIndex) throws Exception {
        Row row = sheet.getRow(rowIndex);
        if (row == null) return;

        Cell questionTextCell = row.getCell(questionTextColumnIndex);
        if (questionTextCell == null) {
            log.debug("第{}行question_text列为空，跳过处理", rowIndex + 1);
            return;
        }

        String questionText = getCellValueAsString(questionTextCell);
        if (questionText == null || questionText.trim().isEmpty()) {
            log.debug("第{}行question_text内容为空，跳过处理", rowIndex + 1);
            return;
        }

        try {
            log.info("正在处理第{}行: {}", rowIndex + 1,
                    questionText.length() > 50 ? questionText.substring(0, 50) + "..." : questionText);

            // 调用OpenAI API
            // String result = openAIClientService.chatCompletion(questionText);

            //调用douBaoAI API
            //String result = douBaoAiClientService.getFormattedQuestionContent(questionText);

            //调用gemini的的评论
            String result = geminiClientService.chatCompletionForComment(questionText);
            QuestionCommentUtil.QuestionComment questionComment = QuestionCommentUtil.parseComment(result);
            result = questionComment.userName() + "\n" + questionComment.comment();

            //调用gemini的解析和答案
//            String result = geminiClientService.chatCompletion(questionText);
//            QuestionSolutionAndAnswerUtil.SolutionAnswer solutionAnswer = QuestionSolutionAndAnswerUtil.parseSolutionAndAnswer(result);
//            if (Objects.isNull(solutionAnswer)){
//                return;
//            }
//            result=solutionAnswer.solution();
            // 同步写入Excel结果列
            synchronized (sheet) {
                int resultColumnIndex = row.getLastCellNum();
                Cell resultCell = row.createCell(resultColumnIndex);
                resultCell.setCellValue(result);
            }

            log.info("第{}行处理完成", rowIndex + 1);
        } catch (Exception e) {
            log.error("处理第{}行时发生错误: {}", rowIndex + 1, e.getMessage());

            // 在结果列写入错误信息
            synchronized (sheet) {
                int resultColumnIndex = row.getLastCellNum();
                Cell resultCell = row.createCell(resultColumnIndex);
                resultCell.setCellValue("ERROR: " + e.getMessage());
            }
        }
    }

    /**
     * 查找指定列名的索引
     */
    private int findColumnIndex(Row headerRow, String columnName) {
        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            Cell cell = headerRow.getCell(i);
            if (cell != null && columnName.equals(getCellValueAsString(cell))) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 获取单元格值为字符串
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) return null;

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                // 对于数字类型，转换为字符串时去掉小数点后的0
                double numericValue = cell.getNumericCellValue();
                if (numericValue == (long) numericValue) {
                    return String.valueOf((long) numericValue);
                } else {
                    return String.valueOf(numericValue);
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
                return "";
            default:
                return "";
        }
    }
}