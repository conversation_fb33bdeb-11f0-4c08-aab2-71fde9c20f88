package com.tal.sea.seaover.application.dto.recall;

import lombok.Data;

/**
 * 召回结果DTO
 */
@Data
public class RecallResult {
    
    /**
     * 以逗号分隔的年级(假如有多个)
     */
    private String grade;
    
    /**
     * 以逗号分隔的知识点名称(假如有多个)
     */
    private String knowledgeName;
    
    /**
     * 父知识点名称
     */
    private String parentKnowledgeName;
    
    /**
     * 以逗号分隔的知识点ID(假如有多个)
     */
    private String knowledgeId;
}