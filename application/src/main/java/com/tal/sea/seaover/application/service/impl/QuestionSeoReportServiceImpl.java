package com.tal.sea.seaover.application.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tal.sea.seaover.application.dto.BingSeoReportRequest;
import com.tal.sea.seaover.application.entity.EduQuestion;
import com.tal.sea.seaover.application.enums.YesNoEnum;
import com.tal.sea.seaover.application.mapper.EduQuestionMapper;
import com.tal.sea.seaover.application.service.BingSeoService;
import com.tal.sea.seaover.application.service.QuestionSeoReportService;
import com.tal.sea.seaover.application.util.SeoUrlUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 问题SEO URL上报服务实现类
 */
@Slf4j
@Service
public class QuestionSeoReportServiceImpl implements QuestionSeoReportService {

    @Autowired
    private EduQuestionMapper eduQuestionMapper;

    @Autowired
    private BingSeoService bingSeoService;

    private static final int PAGE_SIZE = 1000;
    private static final String SEO_URL_PREFIX = "https://www.edu.com/homework-helper";

    @Override
    public String reportToBingSeoUrl(BingSeoReportRequest request) {
        log.info("开始执行SEO URL上报任务，起始ID: {}", request.getId());

        int totalProcessed = 0;
        int totalReported = 0;
        Long lastProcessedId = request.getId(); // 使用请求中的ID作为起始游标
        int batchCount = 0;

        try {
            while (true) {
                batchCount++;

                // 使用基于ID的游标分页查询EduQuestion
                LambdaQueryWrapper<EduQuestion> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(EduQuestion::getAvailable, YesNoEnum.YES.getValue());
                // 如果有lastProcessedId，添加ID大于条件（游标分页）
                if (lastProcessedId != null && lastProcessedId > 0) {
                    queryWrapper.gt(EduQuestion::getId, lastProcessedId);
                }
                Date startTime = request.getStartTime();
                if (!Objects.isNull(startTime)){
                    queryWrapper.gt(EduQuestion::getUpdateTime, startTime);
                }
                // 按ID升序排列，限制查询数量
                queryWrapper.orderByAsc(EduQuestion::getId).last("LIMIT " + PAGE_SIZE);

                List<EduQuestion> questions = eduQuestionMapper.selectList(queryWrapper);

                if (questions.isEmpty()) {
                    log.info("没有更多数据需要处理，退出循环");
                    break;
                }

                log.info("第{}批次，查询到{}条数据，起始ID: {}", batchCount, questions.size(),
                        lastProcessedId != null ? lastProcessedId : "无");

                // 处理当前页的数据
                List<String> seoUrls = new ArrayList<>();

                for (EduQuestion question : questions) {
                    try {
                        // 生成SEO URL
                        String seoUrl = SeoUrlUtil.generateSeoUrl(
                                question.getSubject(),
                                String.valueOf(question.getId()),
                                question.getQuestionText()
                        );

                        if (StringUtils.hasText(seoUrl)) {
                            // 添加前缀
                            String fullSeoUrl = SEO_URL_PREFIX + seoUrl;
                            seoUrls.add(fullSeoUrl);
                            log.debug("生成SEO URL: ID={}, URL={}", question.getId(), fullSeoUrl);
                        } else {
                            log.warn("生成SEO URL失败: ID={}, Subject={}", question.getId(), question.getSubject());
                        }

                        // 更新lastProcessedId为当前处理的问题ID
                        lastProcessedId = question.getId();
                        totalProcessed++;

                    } catch (Exception e) {
                        log.error("处理问题ID={}时发生异常", question.getId(), e);
                        // 即使处理失败，也要更新lastProcessedId以避免重复处理
                        lastProcessedId = question.getId();
                    }
                }

                // 批量上报到必应
                if (!seoUrls.isEmpty()) {
                    try {
                        boolean reportSuccess = bingSeoService.reportUrlsToBing(seoUrls);
                        if (reportSuccess) {
                            totalReported += seoUrls.size();
                        } else {
                            log.error("上报SEO URL到必应失败，本批次{}个URL 停止任务! 最后处理的ID: {}", seoUrls.size(), lastProcessedId);
                            break;
                        }
                    } catch (Exception e) {
                        log.error("上报SEO URL到必应时发生异常", e);
                    }
                }

                // 记录当前批次处理完成的最后ID
                log.info("第{}批次处理完成，最后处理的ID: {}", batchCount, lastProcessedId);

                // 如果当前批次数据少于页大小，说明已经是最后一批
                if (questions.size() < PAGE_SIZE) {
                    log.info("已处理完所有数据，退出循环");
                    break;
                }

                // 添加延迟避免对数据库造成过大压力
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("线程被中断");
                    break;
                }
            }

        } catch (Exception e) {
            log.error("SEO URL上报任务执行异常", e);
            return String.format("任务执行异常: %s, 已处理%d条数据, 已上报%d个URL, 最后处理ID: %s",
                    e.getMessage(), totalProcessed, totalReported, lastProcessedId);
        }

        // 记录最后一次的ID值
        log.info("SEO URL上报任务完成 - 总共处理: {}条数据, 成功上报: {}个URL, 最后处理的ID: {}",
                totalProcessed, totalReported, lastProcessedId);

        return String.format("任务完成 - 总共处理: %d条数据, 成功上报: %d个URL, 最后处理的ID: %s",
                totalProcessed, totalReported, lastProcessedId);
    }
}