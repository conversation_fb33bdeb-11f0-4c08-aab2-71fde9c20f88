package com.tal.sea.seaover.application.util;

import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * 重新打标测试工具类
 */
@Slf4j
public class RelabelingTestUtil {
    
    /**
     * 生成测试用的题目ID列表
     */
    public static List<Long> generateTestQuestionIds() {
        // 这里可以根据实际情况修改测试ID
        return Arrays.asList(1L, 2L, 3L, 4L, 5L);
    }
    
    /**
     * 打印重新打标API调用示例
     */
    public static void printApiExample() {
        log.info("=== 重新打标API调用示例 ===");
        log.info("POST /api/labeling/relabeling");
        log.info("Content-Type: application/json");
        log.info("Request Body:");
        log.info("{");
        log.info("  \"questionIds\": [1, 2, 3, 4, 5]");
        log.info("}");
        log.info("");
        log.info("Response Example:");
        log.info("{");
        log.info("  \"code\": 200,");
        log.info("  \"message\": \"success\",");
        log.info("  \"data\": {");
        log.info("    \"totalCount\": 5,");
        log.info("    \"successCount\": 4,");
        log.info("    \"skippedCount\": 1,");
        log.info("    \"dbUpdateSuccess\": true,");
        log.info("    \"esUpdateSuccess\": true,");
        log.info("    \"duration\": 15000,");
        log.info("    \"message\": \"重新打标成功\"");
        log.info("  }");
        log.info("}");
        log.info("========================");
    }
    
    public static void main(String[] args) {
        printApiExample();
    }
}
