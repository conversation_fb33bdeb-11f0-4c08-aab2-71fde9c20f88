package com.tal.sea.seaover.application.service.data;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tal.sea.seaover.application.config.DouBaoConfig;
import com.tal.sea.seaover.application.dto.ai.DouBaoChatCompletionRequest;
import com.tal.sea.seaover.application.dto.ai.DouBaoChatCompletionResponse;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class DouBaoAiClientService {

    @Autowired
    private DouBaoConfig douBaoConfig;

    private String promotionContent;
    private ObjectMapper objectMapper;

    /**
     * 初始化方法，加载提示语文件和配置ObjectMapper
     */
    @PostConstruct
    public void init() {
        try {
            ClassPathResource resource = new ClassPathResource(douBaoConfig.getPromotionFile());
            try (var inputStream = resource.getInputStream()) {
                this.promotionContent = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
            }
            log.info("成功加载DouBao提示语文件: {}", douBaoConfig.getPromotionFile());
        } catch (IOException e) {
            log.error("加载DouBao提示语文件失败: {}", douBaoConfig.getPromotionFile(), e);
            // 使用默认的提示语内容
            this.promotionContent = getDefaultPromotionContent();
            log.info("使用默认的DouBao提示语内容");
        }

        // 初始化ObjectMapper
        this.objectMapper = new ObjectMapper();
        this.objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * 聊天完成功能
     */
    public String chatCompletion(String mathProblem) throws Exception {
        DouBaoChatCompletionRequest req = createChatRequest();
        req.addMessage("user", mathProblem);
        DouBaoChatCompletionResponse resp = doChatCompletion(req, 3);
        return resp.getMessage() != null ? resp.getMessage().getContent() : "";
    }

    /**
     * 执行聊天完成请求
     */
    public DouBaoChatCompletionResponse doChatCompletion(DouBaoChatCompletionRequest req, int maxRetries) throws Exception {
        HttpClient client = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(120))
                .build();

        // 构建请求数据，使用实际的模型名称
        Map<String, Object> data = new HashMap<>();
        data.put("model", douBaoConfig.getModel());
        data.put("messages", req.getMessages());

        String requestUrl = douBaoConfig.getApiUrl();
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(requestUrl))
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + douBaoConfig.getApiKey())
                .timeout(Duration.ofSeconds(120)) // 设置读取超时（整体请求超时）
                .POST(HttpRequest.BodyPublishers.ofString(objectMapper.writeValueAsString(data)))
                .build();

        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

                if (response.statusCode() == 429) {
                    retryCount++;
                    if (retryCount < maxRetries) {
                        log.warn("DouBao API配额超限，第{}次重试", retryCount);
                        Thread.sleep(1000);
                        continue;
                    }
                    throw new RuntimeException("达到最大重试次数，DouBao API仍然返回配额超限错误");
                }

                if (response.statusCode() != 200) {
                    log.error("DouBao API请求失败，状态码: {}, 响应: {}", response.statusCode(), response.body());
                    throw new RuntimeException("DouBao请求失败，状态码: " + response.statusCode() + ", 响应: " + response.body());
                }

                DouBaoChatCompletionResponse chatResp = objectMapper.readValue(response.body(), DouBaoChatCompletionResponse.class);
                if (chatResp.getId() == null) {
                    throw new IllegalArgumentException("DouBao ChatCompletionResponse: id not found in response");
                }
                
                log.info("DouBao API调用成功，响应ID: {}", chatResp.getId());
                return chatResp;
                
            } catch (Exception e) {
                if (retryCount < maxRetries - 1) {
                    retryCount++;
                    log.warn("DouBao API调用异常，第{}次重试: {}", retryCount, e.getMessage());
                    Thread.sleep(1000);
                    continue;
                } else {
                    throw e;
                }
            }
        }

        throw new RuntimeException("DouBao API调用未预期的错误");
    }

    /**
     * 创建聊天请求，包含系统提示语
     */
    private DouBaoChatCompletionRequest createChatRequest() {
        DouBaoChatCompletionRequest req = new DouBaoChatCompletionRequest(douBaoConfig.getModel());
        req.addMessage("system", promotionContent);
        return req;
    }

    /**
     * 获取默认的提示语内容
     */
    private String getDefaultPromotionContent() {
        return """
            You are an expert in meticulous format rewriting, skilled in formatting and typesetting mathematical problems. I will provide you with questions, and you need to process them around the [core principles] in accordance with the [standardized processing requirements for questions], and strictly output the data in JSON format as per the output example.
            
            # Core Principles
            - The content of the title itself shall not be altered in any way. No additions or reductions are allowed. Only format replacement processing is permitted.
            
            # Requirements for Standardizing Problem Processing
            1. Identify whether the problem is a single question or multiple questions. If it is multiple questions, store each question in the format of an array element.
            2. Formula/Equation Handling: If you need to use LaTeX formulas in your response, please enclose the formulas with "$$".(for example: $$\\frac{3}{10}$$, $$70 \\div 10$$···)
              - Add "\\t" between formulas to avoid connecting different formulas together.
            3. 将题目拆分成题干(stem)和题目问题(content)。
            4. Answer filling symbol processing:
               - Replace the brackets that require filling in the answers with {answer_brackets}。
               - Replace the underlined parts that require filling in the answers with {answer_underline}。
               - If there were no answer brackets or blanks in the question, you cannot add {answer_brackets} and {answer_underline} by yourself.
            5. 题型判断（不允许出现其他type）：
              - 选择题（type：1）、简答题（type：2）、判断题（type：3）、填空题（type：4）、其他（type：5）
            6. 如果是选择题（非选择题不做处理）：
              - 选择题题干处理：在题干后面加上"\\n"，即选项与题干换行。
              - 选项处理：
                - 如果所有选项（包括空格）总字数超过100，在每个选项后添加"\\n".
                - 否则不添加"\\n".
            7. 公式/算式latex格式：将公式/算式中的平方、分数、pi等均渲染成latex格式。
              - 注意：原本的数字2不应该渲染成平方。比如：6 x 82 不能渲染成 6 x 8^{2}
              - 错误示例：x2+y2=5，1/10x1/2，pi
              - 正确示例：x^{2}+y^{2}=5，\\frac{1}{10} \\times \\frac{1}{2}，\\pi
            8. json格式转义：将json中latex的单斜杆"\\"转义成双斜杆"\\\\"。
            
            # 限制
            - 不要输出题目解答的内容。
            - 不能更改题目本身内容，即使它与题目无关。
            
            # JSON格式检查
            - 检查生成的JSON格式是否正确，不正确的JSON需要重新生成。
            
            Please process the mathematical problems according to these requirements and return the result in proper JSON format.
            """;
    }
}