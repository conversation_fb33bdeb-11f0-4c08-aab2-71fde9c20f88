package com.tal.sea.seaover.application.service.data;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tal.sea.seaover.application.config.DouBaoConfig;
import com.tal.sea.seaover.application.dto.ai.DouBaoChatCompletionRequest;
import com.tal.sea.seaover.application.dto.ai.DouBaoChatCompletionResponse;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class DouBaoAiClientService {

    @Autowired
    private DouBaoConfig douBaoConfig;

    private String promotionContent;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 初始化方法，加载提示语文件和配置ObjectMapper
     */
    @PostConstruct
    public void init() {
        try {
            ClassPathResource resource = new ClassPathResource(douBaoConfig.getPromotionFile());
            try (var inputStream = resource.getInputStream()) {
                this.promotionContent = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
            }
            log.info("成功加载DouBao提示语文件: {}", douBaoConfig.getPromotionFile());
        } catch (IOException e) {
            log.error("加载DouBao提示语文件失败: {}", douBaoConfig.getPromotionFile(), e);
            throw new RuntimeException("加载DouBao提示语文件失败", e);
        }
    }

    /**
     * 聊天完成功能
     */
    public String chatCompletion(String mathProblem) throws Exception {
        DouBaoChatCompletionRequest req = createChatRequest();
        req.addMessage("user", mathProblem);
        DouBaoChatCompletionResponse resp = doChatCompletion(req, 3);
        return resp.getMessage() != null ? resp.getMessage().getContent() : "";
    }

    /**
     * 获取格式化的题干内容
     * 调用chatCompletion获取JSON格式返回值
     *
     * @param mathProblem 数学问题
     * @return 拼接后的题干内容 (stem + content)
     * @throws Exception 处理异常
     */
    public String getFormattedQuestionContent(String mathProblem) {
        try {
            // 调用chatCompletion获取JSON格式的返回值
            String jsonResponse = chatCompletion(mathProblem);
            if (StringUtils.isEmpty(jsonResponse)) {
                log.warn("DouBao API返回空结果");
                return mathProblem;
            }
            // 解析JSON并拼接stem和content
            return parseAndConcatenateContent(jsonResponse);
        } catch (Exception e) {
            log.error("获取格式化题干内容失败", e);
            throw new RuntimeException("获取格式化题干内容失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析JSON响应并拼接stem和content
     *
     * @param jsonResponse JSON格式的响应
     * @return 拼接后的内容
     */
    private String parseAndConcatenateContent(String jsonResponse) {
        try {
            // 清理JSON字符串，移除可能的markdown代码块标记
            String cleanJson = cleanJsonResponse(jsonResponse);

            // 使用正则表达式匹配question字段内容
            Pattern pattern = Pattern.compile("\"question\":\\s*\"([^\"]*)\"");
            Matcher matcher = pattern.matcher(cleanJson);

            if (matcher.find()) {
                return matcher.group(1);
            } else {
                throw new RuntimeException("JSON中未找到question字段");
            }
        } catch (Exception e) {
            log.error("解析JSON响应异常: {} 原始JSON内容:{}", e.getMessage(), jsonResponse);
            throw new RuntimeException("解析JSON响应异常");
        }
    }

    /**
     * 清理JSON响应，移除可能的markdown代码块标记
     */
    private String cleanJsonResponse(String jsonResponse) {
        if (jsonResponse == null) {
            return "";
        }

        String cleaned = jsonResponse.trim();

        // 移除markdown代码块标记
        if (cleaned.startsWith("```json")) {
            cleaned = cleaned.substring(7);
        } else if (cleaned.startsWith("```")) {
            cleaned = cleaned.substring(3);
        }

        if (cleaned.endsWith("```")) {
            cleaned = cleaned.substring(0, cleaned.length() - 3);
        }

        return cleaned.trim();
    }

    /**
     * 执行聊天完成请求
     */
    public DouBaoChatCompletionResponse doChatCompletion(DouBaoChatCompletionRequest req, int maxRetries) throws Exception {
        HttpClient client = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(120))
                .build();

        // 构建请求数据，使用实际的模型名称
        Map<String, Object> data = new HashMap<>();
        data.put("model", douBaoConfig.getModel());
        data.put("messages", req.getMessages());

        String requestUrl = douBaoConfig.getApiUrl();
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(requestUrl))
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + douBaoConfig.getApiKey())
                .timeout(Duration.ofSeconds(120)) // 设置读取超时（整体请求超时）
                .POST(HttpRequest.BodyPublishers.ofString(objectMapper.writeValueAsString(data)))
                .build();

        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

                if (response.statusCode() == 429) {
                    retryCount++;
                    if (retryCount < maxRetries) {
                        log.warn("DouBao API配额超限，第{}次重试", retryCount);
                        Thread.sleep(1000);
                        continue;
                    }
                    throw new RuntimeException("达到最大重试次数，DouBao API仍然返回配额超限错误");
                }

                if (response.statusCode() != 200) {
                    log.error("DouBao API请求失败，状态码: {}, 响应: {}", response.statusCode(), response.body());
                    throw new RuntimeException("DouBao请求失败，状态码: " + response.statusCode() + ", 响应: " + response.body());
                }

                DouBaoChatCompletionResponse chatResp = objectMapper.readValue(response.body(), DouBaoChatCompletionResponse.class);
                if (chatResp.getId() == null) {
                    throw new IllegalArgumentException("DouBao ChatCompletionResponse: id not found in response");
                }
                return chatResp;

            } catch (Exception e) {
                if (retryCount < maxRetries - 1) {
                    retryCount++;
                    log.warn("DouBao API调用异常，第{}次重试: {}", retryCount, e.getMessage());
                    Thread.sleep(1000);
                    continue;
                } else {
                    throw e;
                }
            }
        }

        throw new RuntimeException("DouBao API调用未预期的错误");
    }

    /**
     * 创建聊天请求，包含系统提示语
     */
    private DouBaoChatCompletionRequest createChatRequest() {
        DouBaoChatCompletionRequest req = new DouBaoChatCompletionRequest(douBaoConfig.getModel());
        req.addMessage("system", promotionContent);
        return req;
    }
}