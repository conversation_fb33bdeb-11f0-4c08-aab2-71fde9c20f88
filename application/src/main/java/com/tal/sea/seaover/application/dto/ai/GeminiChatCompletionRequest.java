package com.tal.sea.seaover.application.dto.ai;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Gemini聊天完成请求DTO
 */
@Data
public class GeminiChatCompletionRequest {
    
    @JsonProperty("model")
    private String model;
    
    @JsonProperty("messages")
    private List<GeminiMessage> messages;
    
    public GeminiChatCompletionRequest(String model) {
        this.model = model;
        this.messages = new ArrayList<>();
    }
    
    public void addMessage(String role, String content) {
        this.messages.add(new GeminiMessage(role, content));
    }
}