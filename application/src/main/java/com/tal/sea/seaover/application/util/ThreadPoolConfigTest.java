package com.tal.sea.seaover.application.util;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程池配置测试工具类
 */
@Slf4j
public class ThreadPoolConfigTest {
    
    /**
     * 测试线程池配置
     */
    public static void testThreadPoolConfig() {
        // 按照指定配置创建线程池
        int cpuCores = (int) Math.ceil(Runtime.getRuntime().availableProcessors() * 0.5); // 0.5核按1核计算
        ThreadPoolExecutor executorService = new ThreadPoolExecutor(
                cpuCores * 2,       // corePoolSize = 2（1核×2）
                5,                  // maximumPoolSize = 5（内存限制下保守值）
                60L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(100),  // 有界队列防OOM
                new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略：主线程兜底
        );
        
        // 输出配置信息
        log.info("=== 线程池配置测试 ===");
        log.info("系统CPU核心数: {}", Runtime.getRuntime().availableProcessors());
        log.info("计算后的CPU核心数: {} (0.5核按1核计算)", cpuCores);
        log.info("核心线程数: {}", executorService.getCorePoolSize());
        log.info("最大线程数: {}", executorService.getMaximumPoolSize());
        log.info("队列容量: {}", executorService.getQueue().remainingCapacity());
        log.info("拒绝策略: {}", executorService.getRejectedExecutionHandler().getClass().getSimpleName());
        log.info("========================");
        
        // 关闭线程池
        executorService.shutdown();
    }
    
    public static void main(String[] args) {
        testThreadPoolConfig();
    }
}
