package com.tal.sea.seaover.application.controller.inner;

import com.tal.sea.seaover.application.dto.QuestionBatchDeleteRequest;
import com.tal.sea.seaover.application.dto.QuestionBatchDeleteResponse;
import com.tal.sea.seaover.application.exception.BusinessException;
import com.tal.sea.seaover.application.service.EduQuestionService;
import com.tal.sea.seaover.application.service.EsQuestionService;
import com.tal.sea.seaover.application.service.InnerEduQuestionService;
import com.tal.sea.seaover.application.service.data.QuestionOpenAIProcessorService;
import com.tal.sea.seaover.application.service.QuestionSeoReportService;
import com.tal.sea.seaover.application.dto.BingSeoReportRequest;
import com.tal.sea.seaover.application.dto.CleanQuestionTextRequest;
import com.tal.sea.seaover.application.util.ResponseEntity;
import com.tal.sea.seaover.application.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 试题控制器
 */
@Slf4j
@RestController
@RequestMapping("/inner/question")
public class InnerQuestionController {

    @Autowired
    private EduQuestionService questionService;

    @Autowired
    private EsQuestionService esQuestionService;

    @Autowired
    private InnerEduQuestionService innerEduQuestionService;

    @Autowired
    private QuestionOpenAIProcessorService questionOpenAIProcessorService;

    @Autowired
    private QuestionSeoReportService questionSeoReportService;

    /**
     * 批量删除试题
     *
     * @param request 批量删除请求参数
     * @return 删除结果响应
     */
    @PostMapping("/batchDelete")
    public ResponseEntity<QuestionBatchDeleteResponse> batchDeleteQuestions(@Validated @RequestBody QuestionBatchDeleteRequest request) {
        try {
            log.info("接收到批量删除试题请求，题目ID数量：{}", request.getQuestionIds().size());

            // 1. 批量删除数据库中的题目（逻辑删除）
            boolean dbDeleteSuccess = questionService.batchDeleteQuestions(request.getQuestionIds());

            // 2. 批量删除ES中的文档
            boolean esDeleteSuccess = false;
            try {
                esDeleteSuccess = esQuestionService.bulkDeleteDocuments(request.getQuestionIds());
            } catch (Exception e) {
                log.error("删除ES文档失败", e);
                // ES删除失败不影响整体流程，但需要记录日志
            }

            // 3. 构建响应
            return createResponseEntity(request, dbDeleteSuccess, esDeleteSuccess);

        } catch (BusinessException e) {
            log.error("批量删除试题业务异常", e);
            return ResponseUtil.failWith500("批量删除试题失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("批量删除试题系统异常", e);
            return ResponseUtil.failWith500("批量删除试题失败：系统异常");
        }
    }

    /**
     * 清理包含成对\begin和\end的试题
     * 分页查询所有试题，验证questionText中双$$符号包裹的内容是否包含成对的\begin和\end，
     * 如果包含则删除该试题（包括数据库和ES）
     *
     * @return 清理结果响应
     */
    @GetMapping("/cleanBeginEndPairs")
    public ResponseEntity cleanQuestionsWithBeginEndPairs() {
        innerEduQuestionService.cleanQuestionsWithBeginEndPairs();
        return ResponseUtil.successWithoutData();
    }

    /**
     * 处理Excel文件中的问题文本
     * 读取Excel中的question_text列，调用AI API处理，并将结果写入最后一列
     *
     * @param file 上传的Excel文件
     * @return 处理后的Excel文件
     */
    @PostMapping("/processQuestionTextExcel")
    public org.springframework.http.ResponseEntity<byte[]> processQuestionTextExcel(@RequestParam("file") MultipartFile file) {
        try {
            log.info("接收到Excel处理请求，文件名: {}, 大小: {} bytes", file.getOriginalFilename(), file.getSize());
            
            // 调用服务处理Excel文件
            byte[] result = questionOpenAIProcessorService.processExcelFile(file);
            
            // 生成下载文件名
            String originalFilename = file.getOriginalFilename();
            String downloadFilename = generateDownloadFilename(originalFilename);
            
            log.info("Excel文件处理完成，返回文件: {}", downloadFilename);
            
            // 返回处理后的Excel文件
            return org.springframework.http.ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + downloadFilename + "\"")
                    .header(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                    .body(result);
                    
        } catch (IllegalArgumentException e) {
            log.error("Excel文件处理参数错误: {}", e.getMessage());
            return org.springframework.http.ResponseEntity.badRequest()
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN_VALUE)
                    .body(("参数错误: " + e.getMessage()).getBytes());
                    
        } catch (Exception e) {
            log.error("Excel文件处理失败", e);
            return org.springframework.http.ResponseEntity.internalServerError()
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN_VALUE)
                    .body(("处理失败: " + e.getMessage()).getBytes());
        }
    }


    /**
     * 生成下载文件名
     */
    private String generateDownloadFilename(String originalFilename) {
        if (originalFilename == null || originalFilename.isEmpty()) {
            return "processed_questions.xlsx";
        }
        
        // 移除文件扩展名
        String nameWithoutExtension = originalFilename;
        int lastDotIndex = originalFilename.lastIndexOf('.');
        if (lastDotIndex > 0) {
            nameWithoutExtension = originalFilename.substring(0, lastDotIndex);
        }
        
        // 添加处理标识和时间戳
        String timestamp = String.valueOf(System.currentTimeMillis());
        return nameWithoutExtension + "_ai_processed_" + timestamp + ".xlsx";
    }

    private ResponseEntity createResponseEntity(QuestionBatchDeleteRequest request, boolean dbDeleteSuccess, boolean esDeleteSuccess) {
        QuestionBatchDeleteResponse response = new QuestionBatchDeleteResponse();
        response.setDeletedCount(request.getQuestionIds().size());
        response.setDbDeleteSuccess(dbDeleteSuccess);
        response.setEsDeleteSuccess(esDeleteSuccess);

        if (dbDeleteSuccess && esDeleteSuccess) {
            response.setMessage("批量删除成功");
            log.info("批量删除试题成功，数量：{}", request.getQuestionIds().size());
            return ResponseUtil.successWithData(response);
        } else if (dbDeleteSuccess && !esDeleteSuccess) {
            response.setMessage("数据库删除成功，ES删除失败");
            log.warn("批量删除试题部分成功：数据库删除成功，ES删除失败，数量：{}", request.getQuestionIds().size());
            return ResponseUtil.successWithData(response);
        } else {
            response.setMessage("删除失败");
            log.error("批量删除试题失败，数量：{}", request.getQuestionIds().size());
            return ResponseUtil.failWith500("批量删除试题失败");
        }
    }

    /**
     * 上报SEO URL到必应
     * 分页查询EduQuestion，生成SEO URL并上报到必应IndexNow API
     *
     * @param request 上报请求参数，包含可选的起始ID
     * @return 上报结果响应
     */
    @PostMapping("/reportToBingSeoUrl")
    public ResponseEntity<String> reportToBingSeoUrl(@RequestBody BingSeoReportRequest request) {
        try {
            log.info("接收到SEO URL上报请求，起始ID: {}", request.getId());
            
            // 调用服务执行上报任务
            String result = questionSeoReportService.reportToBingSeoUrl(request);
            
            log.info("SEO URL上报任务完成: {}", result);
            
            return ResponseUtil.successWithData(result);
            
        } catch (Exception e) {
            log.error("SEO URL上报任务执行失败", e);
            return ResponseUtil.failWith500("SEO URL上报失败: " + e.getMessage());
        }
    }

    /**
     * 清理题目文本
     * 分页查询EduQuestion，使用DouBao AI格式化题目文本
     *
     * @param request 清理请求参数，包含可选的起始ID
     * @return 清理结果响应
     */
    @PostMapping("/cleanQuestionText")
    public ResponseEntity<String> cleanQuestionText(@RequestBody CleanQuestionTextRequest request) {
        try {
            log.info("接收到题目文本清理请求，起始ID: {}", request.getId());
            
            // 调用服务执行清理任务
            String result = innerEduQuestionService.cleanQuestionText(request);
            
            log.info("题目文本清理任务完成: {}", result);
            
            return ResponseUtil.successWithData(result);
            
        } catch (Exception e) {
            log.error("题目文本清理任务执行失败", e);
            return ResponseUtil.failWith500("题目文本清理失败: " + e.getMessage());
        }
    }


}