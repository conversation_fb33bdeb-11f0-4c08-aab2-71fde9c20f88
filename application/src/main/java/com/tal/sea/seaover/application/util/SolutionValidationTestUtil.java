package com.tal.sea.seaover.application.util;

import lombok.extern.slf4j.Slf4j;

/**
 * Solution验证测试工具类
 */
@Slf4j
public class SolutionValidationTestUtil {
    
    /**
     * 测试用例：有效的solution示例
     */
    public static String getValidSolution() {
        return """
                **step1** 首先分析题目条件
                根据题目描述，我们需要计算 $$x^2 + 2x + 1 = 0$$ 的解。
                
                **step2** 应用求根公式
                使用二次方程求根公式：$$x = \\frac{-b \\pm \\sqrt{b^2-4ac}}{2a}$$
                
                **step3** 计算结果
                代入 $$a=1, b=2, c=1$$，得到 $$x = -1$$
                """;
    }
    
    /**
     * 测试用例：无效的solution示例
     */
    public static void printInvalidSolutionExamples() {
        log.info("=== 无效Solution示例 ===");
        
        // 1. 不以**step1**开头
        log.info("1. 不以**step1**开头的示例：");
        log.info("首先分析题目...");
        log.info("");
        
        // 2. 数学公式未被双$$包裹
        log.info("2. 数学公式未被双$$包裹的示例：");
        log.info("**step1** 计算 $x^2 + 1 = 0$ 的解");
        log.info("");
        
        // 3. 重复步骤
        log.info("3. 重复步骤的示例：");
        log.info("**step1** 第一步\n**step1** 重复的第一步");
        log.info("");
        
        // 4. 包含连续三个---
        log.info("4. 包含连续三个---的示例：");
        log.info("**step1** 分析题目\n---\n这是分隔线");
        log.info("");
        
        log.info("========================");
    }
    
    /**
     * 测试用例：边界情况
     */
    public static void printBorderCases() {
        log.info("=== 边界情况测试 ===");
        
        log.info("1. 空字符串");
        log.info("2. 只有**step1**");
        log.info("3. 包含**Step1**（大小写）");
        log.info("4. 包含**step 1**（带空格）");
        log.info("5. 未配对的$$符号");
        log.info("6. 嵌套的$$符号");
        log.info("7. 多个不同步骤：step1, step2, step3");
        log.info("8. 两个---（不足三个）");
        
        log.info("========================");
    }
    
    /**
     * 验证规则说明
     */
    public static void printValidationRules() {
        log.info("=== Solution验证规则 ===");
        log.info("以下任何条件成立都不符合要求：");
        log.info("1. 开头不是**step1**开头的");
        log.info("2. 数学公式不是被双$$包裹的");
        log.info("3. 出现重复步骤的，比如多个step1字符串");
        log.info("4. 解析中包含连续三个---的");
        log.info("");
        log.info("有效示例：");
        log.info(getValidSolution());
        log.info("========================");
    }
    
    public static void main(String[] args) {
        printValidationRules();
        printInvalidSolutionExamples();
        printBorderCases();
    }
}
