package com.tal.sea.seaover.application.controller.inner;


import com.tal.sea.seaover.application.service.video.VideoAudioProcessorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 视频中音频处理控制器
 */
@RestController
@RequestMapping("/inner/video-audio")
public class InnerVideoAudioProcessorController {

    @Autowired
    private VideoAudioProcessorService videoAudioProcessorService;

    /**
     * 处理视频音频
     *
     * @param file 上传Excel文件
     * @return 处理后的Excel文件
     */
    @PostMapping("/process")
    public ResponseEntity<byte[]> processExcel(@RequestParam("file") MultipartFile file) throws Exception {
        byte[] result = videoAudioProcessorService.processExcelFile(file);
        return ResponseEntity.ok()
                .header("Content-Disposition", "attachment; filename=processed.xlsx")
                .body(result);
    }
}