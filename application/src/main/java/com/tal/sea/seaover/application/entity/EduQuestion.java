package com.tal.sea.seaover.application.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 试题表实体类
 */
@Data
@TableName("edu_question")
public class EduQuestion implements Serializable {
    
    /**
     * 主键ID，自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 来源，gaoth
     */
    private String source;
    
    /**
     * 试题标识
     */
    private String questionId;
    
    /**
     * 题干
     */
    private String questionText;
    
    /**
     * 学科，Math, ELA, Science
     */
    private String subject;
    
    /**
     * 答案
     */
    private String answer;
    
    /**
     * 解析
     */
    private String solution;
    
    /**
     * 年级，多个以逗号分隔
     */
    private String grade;
    
    /**
     * 知识点，多个以逗号分隔
     */
    private String knowledgeId;
    
    /**
     * 知识点名称，多个以逗号分隔
     */
    private String knowledgeName;
    
    /**
     * 父级知识点名称
     */
    private String parentKnowledgeName;
    
    /**
     * 点赞数
     */
    private Integer helpful;
    
    /**
     * 反赞数
     */
    private Integer notHelpful;
    
    /**
     * 删除标志，0-未删除，1-已删除
     */
    @TableLogic(value = "0", delval = "1")
    private Integer delFlag;
    
    /**
     * 是否可用，0-不可用，1-可用（经过数据打标之后才能使用）
     */
    private Integer available;

    /**
     * 创建时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER)
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}