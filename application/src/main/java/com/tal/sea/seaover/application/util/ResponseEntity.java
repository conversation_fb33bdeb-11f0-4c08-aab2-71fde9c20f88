package com.tal.sea.seaover.application.util;

import lombok.Data;

@Data
public class ResponseEntity<T> {
    private String error_reason;
    private String error_msg;
    private String meta_data;
    private String trace_id;
    private Long server_time;
    private T data;

    public ResponseEntity(String error_reason, String error_msg, String meta_data, String trace_id, Long server_time, T data) {
        this.error_reason = error_reason;
        this.error_msg = error_msg;
        this.meta_data = meta_data;
        this.trace_id = trace_id;
        this.server_time = server_time;
        this.data = data;
    }

}
