package com.tal.sea.seaover.application.dto.ai;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * DouBao聊天完成响应DTO
 */
@Data
public class DouBaoChatCompletionResponse {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("object")
    private String object;
    
    @JsonProperty("created")
    private long created;
    
    @JsonProperty("model")
    private String model;
    
    @JsonProperty("choices")
    private List<DouBaoChoice> choices;
    
    @JsonProperty("usage")
    private DouBaoUsage usage;
    
    @JsonProperty("system_fingerprint")
    private String systemFingerprint;
    
    public DouBaoChatCompletionResponse() {
        this.choices = new ArrayList<>();
        this.usage = new DouBaoUsage();
    }
    
    /**
     * 获取第一个选择的消息
     */
    public DouBaoMessage getMessage() {
        return choices.isEmpty() ? null : choices.get(0).getMessage();
    }
}