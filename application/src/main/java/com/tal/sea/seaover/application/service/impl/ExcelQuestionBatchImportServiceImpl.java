package com.tal.sea.seaover.application.service.impl;

import com.tal.sea.seaover.application.dto.ExcelImportResult;
import com.tal.sea.seaover.application.entity.EduQuestion;
import com.tal.sea.seaover.application.service.ExcelQuestionBatchImportService;
import com.tal.sea.seaover.application.service.data.DouBaoAiClientService;
import com.tal.sea.seaover.application.util.QuestionTextFilterUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;

/**
 * Excel题目批量导入服务实现类
 */
@Slf4j
@Service
public class ExcelQuestionBatchImportServiceImpl implements ExcelQuestionBatchImportService {

    @Autowired
    private EduQuestionBatchServiceImpl eduQuestionBatchService;
    @Autowired
    private DouBaoAiClientService douBaoAiClientService;

    private static final String[] REQUIRED_COLUMNS = {"source", "question_id", "question_text", "subject"};

    @Override
    public ExcelImportResult importQuestionsFromExcel(MultipartFile file) {
        ExcelImportResult result = new ExcelImportResult();
        
        try {
            // 1. 验证文件
            if (file == null || file.isEmpty()) {
                result.setSuccess(false);
                result.setErrorMessage("文件不能为空");
                return result;
            }

            // 2. 解析Excel文件
            Workbook workbook = new XSSFWorkbook(file.getInputStream());
            
            List<EduQuestion> validQuestions = new ArrayList<>();
            List<String> validationErrors = new ArrayList<>();
            int totalRows = 0;
            int validCount = 0;
            int failedCount = 0;

            // 3. 遍历所有工作表
            for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
                Sheet sheet = workbook.getSheetAt(sheetIndex);
                log.info("开始处理工作表: {}", sheet.getSheetName());

                if (sheet.getPhysicalNumberOfRows() <= 1) {
                    log.warn("工作表 {} 没有数据行，跳过", sheet.getSheetName());
                    continue;
                }

                // 4. 验证表头
                Row headerRow = sheet.getRow(0);
                Map<String, Integer> columnIndexMap = validateAndGetColumnIndexes(headerRow);
                if (columnIndexMap == null) {
                    result.setSuccess(false);
                    result.setErrorMessage("Excel文件缺少必需的列: " + Arrays.toString(REQUIRED_COLUMNS));
                    workbook.close();
                    return result;
                }

                // 5. 处理数据行
                for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                    Row row = sheet.getRow(rowIndex);
                    if (row == null) continue;

                    totalRows++;
                    
                    try {
                        EduQuestion question = parseRowToQuestion(row, columnIndexMap);
                        if (question != null) {
                            // 调用DouBao AI格式化题目文本
                            String formattedContent = douBaoAiClientService.getFormattedQuestionContent(question.getQuestionText());
                            // 验证question_text
                            boolean isContain = QuestionTextFilterUtil.validateBeginEndPairs(formattedContent);
                            question.setQuestionText(formattedContent);
                            if (isContain) {
                                log.info("第{}行question_text验证不合格: {}", rowIndex + 1, question.getQuestionText());
                                failedCount++;
                            } else {
                                //调用豆包模型对题干进行处理

                                validQuestions.add(question);
                                validCount++;
                            }
                        } else {
                            failedCount++;
                        }
                    } catch (Exception e) {
                        log.error("解析第{}行数据失败: {}", rowIndex + 1, e.getMessage());
                        failedCount++;
                    }
                }
            }

            workbook.close();

            // 6. 批量插入有效数据
            int insertedCount = 0;
            if (!validQuestions.isEmpty()) {
                log.info("开始批量插入{}条有效数据", validQuestions.size());
                insertedCount = eduQuestionBatchService.batchInsert(validQuestions);
            }

            // 7. 设置结果
            result.setSuccess(true);
            result.setTotalRows(totalRows);
            result.setInsertedCount(insertedCount);
            result.setFailedCount(failedCount);

            log.info("Excel导入完成 - 总行数: {}, 有效数据: {}, 成功导入: {}, 失败: {}", 
                    totalRows, validCount, insertedCount, failedCount);

        } catch (IOException e) {
            log.error("读取Excel文件失败", e);
            result.setSuccess(false);
            result.setErrorMessage("读取Excel文件失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("导入Excel数据异常", e);
            result.setSuccess(false);
            result.setErrorMessage("导入数据异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 验证表头并获取列索引映射
     */
    private Map<String, Integer> validateAndGetColumnIndexes(Row headerRow) {
        if (headerRow == null) {
            return null;
        }

        Map<String, Integer> columnIndexMap = new HashMap<>();
        Set<String> foundColumns = new HashSet<>();

        // 遍历表头行，查找必需的列
        for (int cellIndex = 0; cellIndex < headerRow.getLastCellNum(); cellIndex++) {
            Cell cell = headerRow.getCell(cellIndex);
            if (cell != null) {
                String columnName = getCellValueAsString(cell).toLowerCase().trim();
                for (String requiredColumn : REQUIRED_COLUMNS) {
                    if (requiredColumn.equals(columnName)) {
                        columnIndexMap.put(requiredColumn, cellIndex);
                        foundColumns.add(requiredColumn);
                        break;
                    }
                }
            }
        }

        // 检查是否所有必需的列都找到了
        for (String requiredColumn : REQUIRED_COLUMNS) {
            if (!foundColumns.contains(requiredColumn)) {
                log.error("缺少必需的列: {}", requiredColumn);
                return null;
            }
        }

        return columnIndexMap;
    }

    /**
     * 解析行数据为EduQuestion对象
     */
    private EduQuestion parseRowToQuestion(Row row, Map<String, Integer> columnIndexMap) {
        try {
            String source = getCellValueAsString(row.getCell(columnIndexMap.get("source")));
            String questionId = getCellValueAsString(row.getCell(columnIndexMap.get("question_id")));
            String questionText = getCellValueAsString(row.getCell(columnIndexMap.get("question_text")));
            String subject = getCellValueAsString(row.getCell(columnIndexMap.get("subject")));

            // 验证必需字段不为空
            if (!StringUtils.hasText(source) || !StringUtils.hasText(questionId) || 
                !StringUtils.hasText(questionText) || !StringUtils.hasText(subject)) {
                return null;
            }

            EduQuestion question = new EduQuestion();
            question.setSource(source);
            question.setQuestionId(questionId);
            question.setQuestionText(questionText);
            question.setSubject(subject);

            // 设置默认值
            question.setAnswer("");
            question.setSolution("");
            question.setGrade("");
            question.setKnowledgeId("");
            question.setKnowledgeName("");
            question.setParentKnowledgeName("");
            question.setHelpful(0);
            question.setNotHelpful(0);
            question.setAvailable(0);
            question.setDelFlag(0);
            question.setCreateTime(new Date());
            question.setUpdateTime(new Date());

            return question;
        } catch (Exception e) {
            log.error("解析行数据失败", e);
            return null;
        }
    }

    /**
     * 获取单元格值作为字符串
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 处理数字，避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }
}