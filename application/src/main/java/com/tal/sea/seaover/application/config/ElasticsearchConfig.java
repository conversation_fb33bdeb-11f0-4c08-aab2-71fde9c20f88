package com.tal.sea.seaover.application.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.URI;

/**
 * Elasticsearch配置类
 * 使用High Level REST Client兼容ES7
 * Spring Boot 3.0兼容版本
 */
@Slf4j
@Configuration
public class ElasticsearchConfig {

    @Value("${spring.elasticsearch.uris}")
    private String elasticsearchUrl;

    @Value("${spring.elasticsearch.username:}")
    private String username;

    @Value("${spring.elasticsearch.password:}")
    private String password;

    @Bean(name = "elasticsearchClient")
    public RestHighLevelClient elasticsearchClient() {
        log.info("创建Elasticsearch客户端, URL: {}", elasticsearchUrl);

        try {
            URI uri = URI.create(elasticsearchUrl);

            // 创建HttpHost
            HttpHost httpHost = new HttpHost(uri.getHost(), uri.getPort(), uri.getScheme());
            RestClientBuilder builder = RestClient.builder(httpHost);

            // 配置认证（使用独立的用户名密码配置）
            if (username != null && !username.trim().isEmpty() &&
                password != null && !password.trim().isEmpty()) {

                CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                credentialsProvider.setCredentials(AuthScope.ANY,
                        new UsernamePasswordCredentials(username, password));

                builder.setHttpClientConfigCallback(httpClientBuilder ->
                        httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider));

                log.info("已配置ES认证，用户名: {}", username);
            } else {
                log.info("未配置ES认证信息");
            }

            // 配置超时
            builder.setRequestConfigCallback(requestConfigBuilder ->
                    requestConfigBuilder
                            .setConnectTimeout(10000)
                            .setSocketTimeout(30000));

            RestHighLevelClient client = new RestHighLevelClient(builder);
            log.info("Elasticsearch客户端创建成功: {}:{}", uri.getHost(), uri.getPort());

            return client;

        } catch (Exception e) {
            log.error("Elasticsearch客户端创建失败: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create Elasticsearch client", e);
        }
    }
}