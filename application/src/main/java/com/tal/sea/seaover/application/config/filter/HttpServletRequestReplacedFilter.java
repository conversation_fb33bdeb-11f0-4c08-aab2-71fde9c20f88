package com.tal.sea.seaover.application.config.filter;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.codec.Charsets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMethod;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class HttpServletRequestReplacedFilter implements Filter {

    private String contentTypeForm = MediaType.APPLICATION_FORM_URLENCODED_VALUE;
    private String contentTypeJson = MediaType.APPLICATION_JSON_VALUE;

    // 定义二进制文件 Content-Type 集合
    private static final Set<String> BINARY_CONTENT_TYPES = new HashSet<>(Arrays.asList(
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // .xlsx
            "application/vnd.ms-excel", // .xls
            "application/pdf", // .pdf
            "image/jpeg", // .jpg
            "image/png", // .png
            "video/mp4", // .mp4
            "application/octet-stream" // 通用二进制类型
    ));
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        ServletRequest requestWrapper = null;
        HttpServletResponseWrapper responseWrapper = new HttpServletResponseWrapper((HttpServletResponse) response);
        if (request instanceof HttpServletRequest) {
            String method = ((HttpServletRequest) request).getMethod();
            if (RequestMethod.GET.name().equalsIgnoreCase(method)) {
                chain.doFilter(request, responseWrapper);
            } else if (RequestMethod.POST.name().equalsIgnoreCase(method)) {
                String contentType = request.getContentType();
                if (StringUtils.isBlank(contentType)) {
                    chain.doFilter(requestWrapper != null ? requestWrapper : request, responseWrapper);
                } else {
                    if (contentType.equalsIgnoreCase(contentTypeJson) || contentType.contains(contentTypeJson)) {
                        requestWrapper = new HttpServletRequestWrapper((HttpServletRequest) request);
                    } else if (contentType.equalsIgnoreCase(contentTypeForm) || contentType.contains(contentTypeForm)) {
                        requestWrapper = new RepeatParameterRequestWrapper((HttpServletRequest) request);
                    }
                    chain.doFilter(requestWrapper != null ? requestWrapper : request, responseWrapper);
                }
            } else {
                chain.doFilter(request, responseWrapper);
            }
        }
        ServletOutputStream out = response.getOutputStream();
        byte[] respData = responseWrapper.getResponseData();
        String responseContentType = responseWrapper.getContentType();
        // 根据 Content-Type 判断是否为二进制文件
        boolean isBinary = responseContentType != null && BINARY_CONTENT_TYPES.stream()
                .anyMatch(responseContentType::contains);

        if (isBinary) {
            // 对于二进制文件，直接写入字节
            out.write(respData);
        } else {
            // 对于文本内容，按 UTF-8 编码处理
            String resp = new String(respData, Charsets.UTF_8);
            out.write(resp.getBytes(Charsets.UTF_8));
        }

        out.flush();
    }

    @Override
    public void destroy() {

    }

}
