package com.tal.sea.seaover.application.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池定义
 */
@Configuration
public class ThreadPoolConfig {

    @Value("${threadPool.question.corePoolSize:40}")
    private int questionCorePoolSize;

    @Value("${threadPool.question.maxPoolSize:50}")
    private int questionMaxPoolSize;

    @Value("${threadPool.question.queueCapacity:1000}")
    private int questionQueueCapacity;

    @Value("${threadPool.comments.corePoolSize:40}")
    private int commentsCorePoolSize;

    @Value("${threadPool.comments.maxPoolSize:50}")
    private int commentsMaxPoolSize;

    @Value("${threadPool.comments.queueCapacity:1000}")
    private int commentsQueueCapacity;
    @Bean
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(questionCorePoolSize);// 核心线程数
        executor.setMaxPoolSize(questionMaxPoolSize);// 最大线程数
        executor.setQueueCapacity(questionQueueCapacity);// 队列容量
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("edu-question-thread-"); // 线程名称前缀
        // 配置拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 设置MDC Task Decorator
        executor.setTaskDecorator(new MdcTaskDecorator());
        executor.initialize();
        return executor;
    }

    /**
     * 评论生成线程池
     *
     * @return
     */
    @Bean
    public ThreadPoolTaskExecutor commentsTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(commentsCorePoolSize);// 核心线程数
        executor.setMaxPoolSize(commentsMaxPoolSize);// 最大线程数
        executor.setQueueCapacity(commentsQueueCapacity);// 队列容量
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("edu-comments-thread-"); // 线程名称前缀
        // 配置拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 设置MDC Task Decorator
        executor.setTaskDecorator(new MdcTaskDecorator());
        executor.initialize();
        return executor;
    }
}
