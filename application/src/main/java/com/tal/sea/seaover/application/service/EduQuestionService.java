package com.tal.sea.seaover.application.service;

import com.tal.sea.seaover.application.dto.*;
import com.tal.sea.seaover.application.entity.EduQuestion;

import java.util.List;

/**
 * 试题服务接口
 */
public interface EduQuestionService {

    /**
     * 分页查询全部试题
     *
     * @param currentPage 页码
     * @param pageSize    每页大小
     * @return
     */
    List<EduQuestion> listEduAllQuestions(int currentPage, int pageSize);

    /**
     * 分页查询试题列表
     *
     * @param request 查询请求参数
     * @return 试题列表响应
     */
    QuestionListResponse getQuestionList(QuestionListRequest request);

    /**
     * 查询试题详情
     *
     * @param request 详情查询请求参数
     * @return 试题详情响应
     */
    QuestionDetailResponse getQuestionDetail(QuestionDetailRequest request);

    /**
     * 查询相邻试题
     *
     * @param request 相邻试题查询请求参数
     * @return 相邻试题响应
     */
    QuestionAdjacencyResponse getAdjacencyQuestion(QuestionAdjacencyRequest request);

    /**
     * 获取学科列表
     *
     * @return 学科列表响应
     */
    QuestionSubjectsResponse getSubjects();


    /**
     * 根据题目ID批量查询题目
     *
     * @param questionIds 题目ID列表
     * @return 批量查询的题目列表
     */
    List<EduQuestion> batchQueryQuestions(List<String> questionIds);

    /**
     * 根据题目ID批量查询题目
     *
     * @param ids 题目ID列表
     * @return 批量查询的题目列表
     */
    List<EduQuestion> batchQueryByIds(List<String> ids);

    /**
     * 批量删除题目（逻辑删除）
     *
     * @param questionIds 题目ID列表
     * @return 删除结果
     */
    boolean batchDeleteQuestions(List<Long> questionIds);

    /**
     * 获取SEO URL
     *
     * @return SEO URL响应
     */
    ListSeoUrlResponse ListSeoUrl(QuestionSeoUrlRequest request);

}