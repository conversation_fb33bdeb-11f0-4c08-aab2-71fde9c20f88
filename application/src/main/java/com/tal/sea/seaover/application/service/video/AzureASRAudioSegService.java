package com.tal.sea.seaover.application.service.video;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.microsoft.cognitiveservices.speech.PropertyId;
import com.microsoft.cognitiveservices.speech.ResultReason;
import com.microsoft.cognitiveservices.speech.SpeechConfig;
import com.microsoft.cognitiveservices.speech.SpeechRecognizer;
import com.microsoft.cognitiveservices.speech.audio.AudioConfig;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Semaphore;

@Service
@Slf4j
public class AzureASRAudioSegService {

    private SpeechConfig speechConfig;
    @Value("${video.speech.key}")
    private String speechKey;
    @Value("${video.speech.region}")
    private String speechRegion;

    @PostConstruct
    public void init() {
        speechConfig = SpeechConfig.fromSubscription(speechKey, speechRegion);
        speechConfig.setSpeechRecognitionLanguage("en-US");
        speechConfig.setProperty(PropertyId.SpeechServiceResponse_JsonResult, "true");
        speechConfig.setProperty(PropertyId.SpeechServiceResponse_RequestWordLevelTimestamps, "true");
        speechConfig.setProperty(PropertyId.SpeechServiceResponse_RequestSentenceBoundary, "true");
        speechConfig.setProperty(PropertyId.SpeechServiceConnection_EndSilenceTimeoutMs, "15000");
        speechConfig.enableDictation();
    }

    /**
     * 获取音频的时间和文本的分隔文本
     *
     * @param wavFilePath
     * @return
     * @throws Exception
     */
    public String getAudioSegmentPlus(String wavFilePath) throws Exception {
        List<String> results = Collections.synchronizedList(new ArrayList<>());
        Semaphore stopSemaphore = new Semaphore(0);

        try (AudioConfig audioInput = AudioConfig.fromWavFileInput(wavFilePath);
             SpeechRecognizer recognizer = new SpeechRecognizer(speechConfig, audioInput)) {

            recognizer.recognizing.addEventListener((s, e) -> {
                if (e.getResult().getReason() == ResultReason.RecognizingSpeech) {
                    log.info("Interim: {}", e.getResult().getText());
                }
            });

            recognizer.recognized.addEventListener((s, e) -> {
                if (e.getResult().getReason() == ResultReason.RecognizedSpeech) {
                    String json = e.getResult().getProperties().getProperty(PropertyId.SpeechServiceResponse_JsonResult);
                    log.info("Raw JSON: {}", json);
                    results.add(json);
                }
            });

            recognizer.canceled.addEventListener((s, e) -> {
                log.error("Recognition canceled: {}", e.getErrorDetails());
                stopSemaphore.release();
            });

            recognizer.sessionStopped.addEventListener((s, e) -> {
                log.info("Session stopped.");
                stopSemaphore.release();
            });

            recognizer.startContinuousRecognitionAsync().get();
            stopSemaphore.acquire();
            recognizer.stopContinuousRecognitionAsync().get();
        }

        return formatResults(results);
    }

    // 格式化结果为 MM:SS 文本
    private String formatResults(List<String> results) {
        List<String> formattedResults = new ArrayList<>();
        JsonParser parser = new JsonParser();

        for (String result : results) {
            JsonElement element = parser.parse(result);
            JsonObject jsonObject = element.getAsJsonObject();
            if (!jsonObject.get("RecognitionStatus").getAsString().equals("Success")) {
                continue;
            }
            String displayText = jsonObject.get("DisplayText").getAsString();
            if (StringUtils.isEmpty(displayText)) {
                continue;
            }
            long startTimeMs = jsonObject.get("Offset").getAsLong() / 10_000;
            if (startTimeMs != -1) {
                String startTime = formatTime(startTimeMs);
                formattedResults.add(startTime + "    " + displayText.trim());
            }
        }
        return String.join("\n", formattedResults);
    }

    // 将毫秒转换为 MM:SS 格式
    private String formatTime(long milliseconds) {
        Duration duration = Duration.ofMillis(milliseconds);
        long seconds = duration.getSeconds();
        long minutes = seconds / 60;
        seconds = seconds % 60;
        return String.format("%02d:%02d", minutes, seconds);
    }
}
