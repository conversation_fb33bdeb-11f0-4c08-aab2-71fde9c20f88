package com.tal.sea.seaover.application.service;

import com.tal.sea.seaover.application.dto.EduEsQuestionResponse;
import com.tal.sea.seaover.application.dto.SimilarQuestionRequest;
import com.tal.sea.seaover.application.dto.es.EduEsQuestionDto;
import com.tal.sea.seaover.application.entity.EduQuestion;

import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * ES题目服务接口
 */
public interface EsQuestionService {

    /**
     * 创建索引
     */
    boolean createIndex() throws IOException;

    /**
     * 删除索引
     */
    boolean deleteIndex() throws IOException;

    /**
     * 批量添加文档
     */
    boolean bulkAddDocuments(List<EduEsQuestionDto> questions) throws IOException;

    /**
     * 根据题目ID推荐相似题目
     *
     * @param request 包含questionId的请求参数
     * @return 相似题目列表
     * @throws IOException ES操作异常
     */
    List<EduEsQuestionResponse> findSimilarQuestions(SimilarQuestionRequest request) throws IOException;

    /**
     * 批量添加题目到ES
     *
     * @param date
     */
    void batchAddQuestionDoc(Date date);

    /**
     * 批量删除ES文档
     *
     * @param questionIds 题目ID列表
     * @return 删除结果
     * @throws IOException ES操作异常
     */
    boolean bulkDeleteDocuments(List<Long> questionIds) throws IOException;

    /**
     * 根据题目ID列表批量添加ES文档
     *
     * @param questions 题目ID列表
     * @return 添加结果
     * @throws IOException ES操作异常
     */
    boolean batchAddQuestion(List<EduQuestion> questions) throws IOException;

}