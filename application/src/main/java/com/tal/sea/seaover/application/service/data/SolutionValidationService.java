package com.tal.sea.seaover.application.service.data;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tal.sea.seaover.application.config.SolutionValidationConfig;
import com.tal.sea.seaover.application.entity.EduQuestion;
import com.tal.sea.seaover.application.mapper.EduQuestionMapper;
import com.tal.sea.seaover.application.util.QuestionSolutionProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Solution验证服务
 */
@Slf4j
@Service
public class SolutionValidationService {
    
    @Autowired
    private EduQuestionMapper eduQuestionMapper;
    
    @Autowired
    private SolutionValidationConfig config;
    
    /**
     * 处理试题solution验证
     * 分页查询未删除、有效的试题，验证solution格式，不符合规范的设置为不可用
     * 
     * @return 处理结果统计
     */
    @Transactional(rollbackFor = Exception.class)
    public SolutionValidationResult handleQuestionSolution() {
        log.info("开始处理试题solution验证任务");
        
        SolutionValidationResult result = new SolutionValidationResult();
        int currentPage = 1;
        
        while (currentPage <= config.getMaxPageLimit()) {
            // 分页查询未删除、有效的试题
            Page<EduQuestion> page = getValidQuestions(currentPage, config.getPageSize());
            List<EduQuestion> questions = page.getRecords();
            
            if (questions.isEmpty()) {
                log.info("没有更多需要处理的试题数据，任务完成");
                break;
            }
            
            log.info("第{}页，获取到{}条试题数据", currentPage, questions.size());
            
            // 处理当前批次的数据
            SolutionValidationResult batchResult = processBatchQuestions(questions);
            result.merge(batchResult);
            
            log.info("第{}页处理完成，当前进度 - 总处理: {}, 无效: {}, 已更新: {}", 
                    currentPage, result.getTotalProcessed(), result.getTotalInvalid(), result.getTotalUpdated());
            
            // 如果当前页数据少于页面大小，说明已经是最后一页
            if (questions.size() < config.getPageSize()) {
                break;
            }
            
            currentPage++;
        }
        
        log.info("试题solution验证任务完成，总处理: {}, 无效: {}, 已更新: {}", 
                result.getTotalProcessed(), result.getTotalInvalid(), result.getTotalUpdated());
        
        return result;
    }
    
    /**
     * 处理批次试题
     * 
     * @param questions 试题列表
     * @return 批次处理结果
     */
    private SolutionValidationResult processBatchQuestions(List<EduQuestion> questions) {
        SolutionValidationResult result = new SolutionValidationResult();
        List<Long> invalidQuestionIds = new ArrayList<>();
        
        for (EduQuestion question : questions) {
            try {
                result.incrementProcessed();
                
                boolean isInvalid = processQuestionSolution(question);
                if (isInvalid) {
                    result.incrementInvalid();
                    invalidQuestionIds.add(question.getId());
                    
                    if (config.isEnableDetailLog()) {
                        log.debug("试题ID: {} solution验证失败", question.getId());
                    }
                } else {
                    if (config.isEnableDetailLog()) {
                        log.debug("试题ID: {} solution验证通过", question.getId());
                    }
                }
                
            } catch (Exception e) {
                log.error("试题ID: {} 处理失败，错误信息: {}", question.getId(), e.getMessage(), e);
            }
        }
        
        // 批量更新无效的试题
        if (!invalidQuestionIds.isEmpty()) {
            int updatedCount = batchUpdateQuestionAvailability(invalidQuestionIds, 0);
            result.setTotalUpdated(updatedCount);
            log.info("批量更新{}条无效试题，成功更新{}条", invalidQuestionIds.size(), updatedCount);
        }
        
        return result;
    }
    
    /**
     * 分页查询未删除、有效的试题
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页数据
     */
    private Page<EduQuestion> getValidQuestions(int pageNum, int pageSize) {
        Page<EduQuestion> page = new Page<>(pageNum, pageSize);
        
        QueryWrapper<EduQuestion> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag", 0)      // 未删除
                   .eq("available", 1)      // 有效（可用）
                   .isNotNull("solution")   // solution不为空
                   .ne("solution", "")      // solution不为空字符串
                   .orderByAsc("id");       // 按ID升序排列
        
        return eduQuestionMapper.selectPage(page, queryWrapper);
    }
    
    /**
     * 处理单个试题的solution验证
     * 
     * @param question 试题对象
     * @return true-solution无效，false-solution有效
     */
    private boolean processQuestionSolution(EduQuestion question) {
        String solution = question.getSolution();
        if (StringUtils.isBlank(solution)) {
            return false;
        }
        // 使用QuestionSolutionProcessor验证solution格式
        boolean isValid = QuestionSolutionProcessor.validateSolution(question.getId(), solution);
        return !isValid; // 返回是否无效
    }
    
    /**
     * 批量更新试题的可用性状态
     * 
     * @param questionIds 试题ID列表
     * @param available 可用性状态（0-不可用，1-可用）
     * @return 成功更新的数量
     */
    private int batchUpdateQuestionAvailability(List<Long> questionIds, Integer available) {
        try {
            UpdateWrapper<EduQuestion> updateWrapper = new UpdateWrapper<>();
            updateWrapper.in("id", questionIds)
                        .set("available", available)
                        .set("update_time", new Date());
            
            int updateCount = eduQuestionMapper.update(null, updateWrapper);
            log.info("批量更新试题可用性，影响行数: {}", updateCount);
            return updateCount;
            
        } catch (Exception e) {
            log.error("批量更新试题可用性时发生异常: {}", e.getMessage(), e);
            return 0;
        }
    }
    
    /**
     * Solution验证结果统计类
     */
    public static class SolutionValidationResult {
        private int totalProcessed = 0;
        private int totalInvalid = 0;
        private int totalUpdated = 0;
        
        public void incrementProcessed() {
            totalProcessed++;
        }
        
        public void incrementInvalid() {
            totalInvalid++;
        }
        
        public void merge(SolutionValidationResult other) {
            this.totalProcessed += other.totalProcessed;
            this.totalInvalid += other.totalInvalid;
            this.totalUpdated += other.totalUpdated;
        }
        
        // Getters and Setters
        public int getTotalProcessed() { return totalProcessed; }
        public int getTotalInvalid() { return totalInvalid; }
        public int getTotalUpdated() { return totalUpdated; }
        public void setTotalUpdated(int totalUpdated) { this.totalUpdated = totalUpdated; }
    }
}
