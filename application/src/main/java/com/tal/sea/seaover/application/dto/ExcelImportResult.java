package com.tal.sea.seaover.application.dto;

import lombok.Data;

/**
 * Excel导入结果统计
 */
@Data
public class ExcelImportResult {
    
    /**
     * 总解析行数
     */
    private int totalRows = 0;
    
    /**
     * 成功插入的数据条数
     */
    private int insertedCount = 0;
    
    /**
     * 过滤掉的重复数据条数
     */
    private int filteredCount = 0;
    
    /**
     * 解析失败的数据条数
     */
    private int failedCount = 0;
    
    /**
     * 是否成功
     */
    private boolean success = true;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 获取有效数据条数（总解析行数 - 解析失败条数）
     */
    public int getValidCount() {
        return totalRows - failedCount;
    }
    
    /**
     * 增加总行数
     */
    public void incrementTotalRows() {
        this.totalRows++;
    }
    
    /**
     * 增加失败数
     */
    public void incrementFailedCount() {
        this.failedCount++;
    }
}
