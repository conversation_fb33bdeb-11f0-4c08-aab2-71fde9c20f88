package com.tal.sea.seaover.application.controller.inner;

import com.tal.sea.seaover.application.service.comment.CommentsService;
import com.tal.sea.seaover.application.util.ResponseEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 评论管理内部接口控制器
 */
@Slf4j
@RestController
@RequestMapping("/inner/comments")
public class InnerCommentsController {

    @Autowired
    private CommentsService commentsService;

    /**
     * 手动为指定试题生成评论
     *
     * @param questionId 试题ID
     * @return 生成结果
     */
    @PostMapping("/generate/{questionId}")
    public ResponseEntity generateCommentsForQuestion(@PathVariable Long questionId) {
        return commentsService.generateCommentsForQuestion(questionId);
    }

    /**
     * 查询指定试题的评论
     *
     * @param questionId 试题ID
     * @return 评论列表
     */
    @GetMapping("/question/{questionId}")
    public ResponseEntity getCommentsByQuestionId(@PathVariable Long questionId) {
        return commentsService.getCommentsByQuestionId(questionId);
    }

    /**
     * 删除指定试题的所有评论
     *
     * @param questionId 试题ID
     * @return 删除结果
     */
    @DeleteMapping("/question/{questionId}")
    public ResponseEntity deleteCommentsByQuestionId(@PathVariable Long questionId) {
        return commentsService.deleteCommentsByQuestionId(questionId);
    }

    /**
     * 根据评论时间更新试题的修改时间
     * 查询最近12小时内的评论，如果评论时间晚于试题的修改时间，则更新试题的修改时间
     *
     * @return 更新结果
     */
    @GetMapping("/updateQuestionModifyTime")
    public ResponseEntity updateQuestionModifyTime() {
        return commentsService.updateQuestionModifyTime();
    }
}