package com.tal.sea.seaover.application.util;

import org.springframework.util.StringUtils;

/**
 * SEO URL生成工具类
 */
public class SeoUrlUtil {
    
    /**
     * 生成SEO URL
     * 格式：/学科(小写)/question/ID值/过滤questionText之后的值(最多长度120)
     * 
     * @param subject 学科
     * @param questionId 题目ID
     * @param questionText 题目文本
     * @return SEO URL
     */
    public static String generateSeoUrl(String subject, String questionId, String questionText) {
        if (!StringUtils.hasText(subject) || !StringUtils.hasText(questionId)) {
            return "";
        }
        
        // 1. 学科转小写
        String subjectLower = subject.toLowerCase();
        
        // 2. 过滤questionText
        String filteredText = "";
        if (StringUtils.hasText(questionText)) {
            filteredText = QuestionTextFilterUtil.filterQuestionText(questionText);
            // 限制长度为120
            if (filteredText.length() > 120) {
                filteredText = filteredText.substring(0, 120);
            }
        }
        
        // 3. 拼接URL
        StringBuilder seoUrl = new StringBuilder();
        seoUrl.append("/").append(subjectLower)
              .append("/question/")
              .append(questionId);
        
        if (StringUtils.hasText(filteredText)) {
            seoUrl.append("/").append(filteredText);
        }
        
        return seoUrl.toString();
    }
}