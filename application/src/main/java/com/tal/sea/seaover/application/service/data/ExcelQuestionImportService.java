package com.tal.sea.seaover.application.service.data;

import com.tal.sea.seaover.application.dto.ExcelImportResult;
import com.tal.sea.seaover.application.entity.EduQuestion;
import com.tal.sea.seaover.application.enums.YesNoEnum;
import com.tal.sea.seaover.application.service.EduQuestionBatchService;
import com.tal.sea.seaover.application.service.EduQuestionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Excel题目数据导入工具类
 */
@Slf4j
@Component
public class ExcelQuestionImportService {

    @Autowired
    private EduQuestionBatchService eduQuestionBatchService;

    @Autowired
    private EduQuestionService questionService;
    @Value("${question.is.enable.filter:false}")
    private boolean enableFilter;

    // Excel列索引常量 - 对应grth_all.xlsx的列结构
    private static final int ID_COLUMN = 0;                    // id
    private static final int SOURCE_COLUMN = 1;               // source
    private static final int QUESTION_ID_COLUMN = 2;          // question_id
    private static final int QUESTION_TEXT_COLUMN = 3;        // question_text
    private static final int SUBJECT_COLUMN = 4;              // subject
    private static final int ANSWER_COLUMN = 5;               // answer
    private static final int SOLUTION_COLUMN = 6;             // solution
    private static final int GRADE_COLUMN = 7;                // grade
    private static final int KNOWLEDGE_ID_COLUMN = 8;         // knowledge_id
    private static final int KNOWLEDGE_NAME_COLUMN = 9;       // knowledge_name
    private static final int PARENT_KNOWLEDGE_NAME_COLUMN = 10; // parent_knowledge_name
    private static final int HELPFUL_COLUMN = 11;             // helpful
    private static final int NOT_HELPFUL_COLUMN = 12;         // not_helpful
    private static final int AVAILABLE_COLUMN = 13;           // available
    private static final int DEL_FLAG_COLUMN = 14;            // del_flag
    private static final int CREATE_TIME_COLUMN = 15;         // create_time
    private static final int UPDATE_TIME_COLUMN = 16;         // update_time

    /**
     * 从grth_all.xlsx文件导入题目数据
     *
     * @return 导入结果统计
     */
    public ExcelImportResult importQuestionsFromGrthExcel() {
        return importQuestionsFromExcel("excel/edu_question_all.xlsx");
    }

    /**
     * 从Excel文件导入题目数据
     *
     * @param filePath Excel文件路径
     * @return 导入结果统计
     */
    public ExcelImportResult importQuestionsFromExcel(String filePath) {
        log.info("开始从Excel文件导入题目数据，文件路径：{}", filePath);

        ExcelImportResult result = new ExcelImportResult();

        try {
            List<EduQuestion> questions = parseExcelFile(filePath, result);
            if (questions.isEmpty()) {
                log.warn("Excel文件中没有有效的题目数据");
                result.setErrorMessage("Excel文件中没有有效的题目数据");
                return result;
            }

            log.info("Excel解析完成，共解析出{}条有效数据", questions.size());

            //根据questionId查询试题是否存在，存在就过滤掉
            if (enableFilter) {
                filterExistQuestion(questions, result);
            }
            // 批量插入数据
            if (!questions.isEmpty()) {
                int insertedCount = eduQuestionBatchService.batchInsert(questions);
                result.setInsertedCount(insertedCount);
                log.info("Excel数据导入完成，成功导入{}条数据", insertedCount);
            }

        } catch (Exception e) {
            log.error("Excel导入失败: {}", e.getMessage(), e);
            result.setErrorMessage("Excel导入失败: " + e.getMessage());
            result.setSuccess(false);
        }

        return result;
    }


    private void filterExistQuestion(List<EduQuestion> questions, ExcelImportResult result) {
        final int BATCH_SIZE = 100;
        Set<String> existQuestionIds = new HashSet<>();
        List<String> questionIds = questions.stream().map(EduQuestion::getQuestionId).collect(Collectors.toList());

        // Process in batches of 100
        for (int i = 0; i < questionIds.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, questionIds.size());
            List<String> batchQuestionIds = questionIds.subList(i, endIndex);

            // Query existing questions for current batch
            List<EduQuestion> existQuestions = questionService.batchQueryQuestions(batchQuestionIds);
            existQuestionIds.addAll(existQuestions.stream()
                    .map(EduQuestion::getQuestionId)
                    .collect(Collectors.toSet()));
        }

        // Remove existing questions
        int initialSize = questions.size();
        questions.removeIf(question -> existQuestionIds.contains(question.getQuestionId()));

        int filteredCount = initialSize - questions.size();
        result.setFilteredCount(filteredCount);
        log.info("滤掉{}条重复数据", filteredCount);
    }

    /**
     * 解析Excel文件
     *
     * @param filePath Excel文件路径
     * @param result   导入结果统计对象
     * @return EduQuestion列表
     */
    private List<EduQuestion> parseExcelFile(String filePath, ExcelImportResult result) throws IOException {
        List<EduQuestion> questions = new ArrayList<>();

        ClassPathResource resource = new ClassPathResource(filePath);

        try (InputStream inputStream = resource.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0); // 读取第一个工作表
            int totalRows = sheet.getLastRowNum() + 1;

            log.info("Excel文件共有{}行数据（包含标题行）", totalRows);

            // 从第二行开始读取数据（跳过标题行）
            for (int rowIndex = 1; rowIndex < totalRows; rowIndex++) {
                result.incrementTotalRows(); // 统计总行数
                //打印解析到第几行
                log.info("正在解析第{}行", rowIndex + 1);
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    log.info("第{}行为空，跳过", rowIndex + 1);
                    result.incrementFailedCount();
                    continue;
                }

                EduQuestion question = parseRowToEduQuestion(row, rowIndex + 1);
                if (question != null) {
                    questions.add(question);
                } else {
                    result.incrementFailedCount();
                }
            }

        } catch (IOException e) {
            log.error("读取Excel文件失败，文件路径：{}，错误信息：{}", filePath, e.getMessage(), e);
            if (e.getMessage().contains("class path resource") && e.getMessage().contains("cannot be opened")) {
                throw new RuntimeException("Excel文件不存在，请确保文件路径正确：" + filePath, e);
            } else {
                throw new RuntimeException("读取Excel文件失败：" + e.getMessage(), e);
            }
        }

        return questions;
    }

    /**
     * 将Excel行数据转换为EduQuestion对象
     *
     * @param row       Excel行
     * @param rowNumber 行号（用于日志）
     * @return EduQuestion对象，如果数据无效则返回null
     */
    private EduQuestion parseRowToEduQuestion(Row row, int rowNumber) {
        try {
            // 读取Excel列数据 - 按照grth_all.xlsx的列结构
            String id = getCellStringValue(row.getCell(ID_COLUMN));
            String source = getCellStringValue(row.getCell(SOURCE_COLUMN));
            String questionId = getCellStringValue(row.getCell(QUESTION_ID_COLUMN));
            String questionText = getCellStringValue(row.getCell(QUESTION_TEXT_COLUMN));
            String subject = getCellStringValue(row.getCell(SUBJECT_COLUMN));
            String answer = getCellStringValue(row.getCell(ANSWER_COLUMN));
            String solution = getCellStringValue(row.getCell(SOLUTION_COLUMN));
            String grade = getCellStringValue(row.getCell(GRADE_COLUMN));
            String knowledgeId = getCellStringValue(row.getCell(KNOWLEDGE_ID_COLUMN));
            String knowledgeName = getCellStringValue(row.getCell(KNOWLEDGE_NAME_COLUMN));
            String parentKnowledgeName = getCellStringValue(row.getCell(PARENT_KNOWLEDGE_NAME_COLUMN));
            String helpful = getCellStringValue(row.getCell(HELPFUL_COLUMN));
            String notHelpful = getCellStringValue(row.getCell(NOT_HELPFUL_COLUMN));
            String available = getCellStringValue(row.getCell(AVAILABLE_COLUMN));
            String delFlag = getCellStringValue(row.getCell(DEL_FLAG_COLUMN));

            // 验证必填字段
            if (StringUtils.isBlank(questionText)) {
                log.warn("第{}行question_text列为空，跳过该行", rowNumber);
                return null;
            }
            if (StringUtils.isBlank(questionId)) {
                log.warn("第{}行question_id列为空，跳过该行", rowNumber);
                return null;
            }
            if (StringUtils.isBlank(source)) {
                log.warn("第{}行source列为空，跳过该行", rowNumber);
                return null;
            }
            if (StringUtils.isBlank(subject)) {
                log.warn("第{}行subject列为空，跳过该行", rowNumber);
                return null;
            }

            // 创建EduQuestion对象并设置字段值
            EduQuestion question = new EduQuestion();

            // 映射Excel列到EduQuestion字段
            question.setQuestionText(questionText.trim());
            question.setQuestionId(questionId.trim());
            question.setSource(source.trim());
            question.setSubject(subject.trim());

            // 设置其他字段，如果为空则使用默认值
            question.setAnswer(StringUtils.isNotBlank(answer) ? answer.trim() : "");
            question.setSolution(StringUtils.isNotBlank(solution) ? solution.trim() : "");
            question.setGrade(StringUtils.isNotBlank(grade) ? grade.trim() : "");
            question.setKnowledgeId(StringUtils.isNotBlank(knowledgeId) ? knowledgeId.trim() : "");
            question.setKnowledgeName(StringUtils.isNotBlank(knowledgeName) ? knowledgeName.trim() : "");
            question.setParentKnowledgeName(StringUtils.isNotBlank(parentKnowledgeName) ? parentKnowledgeName.trim() : "");

            // 处理数字字段
            try {
                question.setHelpful(StringUtils.isNotBlank(helpful) ? Integer.parseInt(helpful.trim()) : 0);
            } catch (NumberFormatException e) {
                question.setHelpful(0);
            }

            try {
                question.setNotHelpful(StringUtils.isNotBlank(notHelpful) ? Integer.parseInt(notHelpful.trim()) : 0);
            } catch (NumberFormatException e) {
                question.setNotHelpful(0);
            }

            // 处理available字段
            try {
                question.setAvailable(StringUtils.isNotBlank(available) ? Integer.parseInt(available.trim()) : YesNoEnum.NO.getValue());
            } catch (NumberFormatException e) {
                question.setAvailable(YesNoEnum.NO.getValue());
            }

            // 处理delFlag字段
            try {
                question.setDelFlag(StringUtils.isNotBlank(delFlag) ? Integer.parseInt(delFlag.trim()) : YesNoEnum.NO.getValue());
            } catch (NumberFormatException e) {
                question.setDelFlag(YesNoEnum.NO.getValue());
            }

            return question;

        } catch (Exception e) {
            log.error("第{}行数据解析失败，错误信息：{}", rowNumber, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取单元格的字符串值
     *
     * @param cell 单元格
     * @return 字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                // 如果是数字，转换为字符串
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 避免科学计数法，直接转换为整数字符串（如果是整数）
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }
}
