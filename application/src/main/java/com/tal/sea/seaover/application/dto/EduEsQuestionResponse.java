package com.tal.sea.seaover.application.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * ES题目
 */
@Data
public class EduEsQuestionResponse {
    private Long id;
    private String source;
    private String questionId;
    private String questionText;
    private String filterQuestionText;
    private String subject;
    private String answer;
    private String solution;
    private List<String> grade;
    private List<String> knowledgeId;
    private List<String> knowledgeName;
    private String parentKnowledgeName;
    private Integer helpful;
    private Integer notHelpful;
    private Date createTime;
    private Date updateTime;
    private Float score; // ES搜索评分
    private String matchReason; // 匹配原因
}