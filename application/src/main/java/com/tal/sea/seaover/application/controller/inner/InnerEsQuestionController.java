package com.tal.sea.seaover.application.controller.inner;

import com.tal.sea.seaover.application.service.EsQuestionService;
import com.tal.sea.seaover.application.util.ResponseEntity;
import com.tal.sea.seaover.application.util.ResponseUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * ES题目操作控制器
 */
@Slf4j
@RestController
@RequestMapping("/inner/question/es")
public class InnerEsQuestionController {

    @Autowired
    private EsQuestionService esQuestionService;
    @Resource
    private RestHighLevelClient elasticsearchClient;


    /**
     * 创建索引
     */
    @GetMapping("/index/create")
    public ResponseEntity createIndex() {
        try {
            boolean success = esQuestionService.createIndex();
            return ResponseUtil.successWithData(success);
        } catch (IOException e) {
            log.error("创建索引异常", e);
            return ResponseUtil.failWith500("创建索引异常：" + e.getMessage());
        }
    }

    /**
     * 删除索引
     */
    @GetMapping("/index/delete")
    public ResponseEntity deleteIndex() {
        try {
            boolean success = esQuestionService.deleteIndex();
            return ResponseUtil.successWithData(success);
        } catch (IOException e) {
            log.error("创建索引异常", e);
            return ResponseUtil.failWith500("创建索引异常：" + e.getMessage());
        }
    }

    /**
     * 添加全部可用的试题到ES
     */
    @GetMapping("/addDoc")
    public ResponseEntity batchAddQuestionDoc() {
        esQuestionService.batchAddQuestionDoc(null);
        return ResponseUtil.successWithoutData();
    }


    /**
     * 测试ES连接
     */
    @GetMapping("/ping")
    public Map<String, Object> ping() {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean isConnected = elasticsearchClient.ping(RequestOptions.DEFAULT);
            result.put("success", true);
            result.put("connected", isConnected);
            result.put("message", isConnected ? "ES连接成功" : "ES连接失败");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "ES连接异常: " + e.getMessage());
        }
        return result;
    }

}