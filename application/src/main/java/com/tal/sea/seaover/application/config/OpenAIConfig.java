package com.tal.sea.seaover.application.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * OpenAI配置类
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "openai")
public class OpenAIConfig {
    
    /**
     * OpenAI API URL
     */
    private String apiUrl="http://ai-service.tal.com/openai-compatible/v1/chat/completions";
    
    /**
     * OpenAI模型名称
     */
    private String model="gpt-4o";
    
    /**
     * API密钥
     */
    private String apiKey="1000080734:db6650536fad0230552699cab258b6e4";
    
    /**
     * 提示语文件路径
     */
    private String promotionFile = "promotion/openai-math-promotion.txt";
}