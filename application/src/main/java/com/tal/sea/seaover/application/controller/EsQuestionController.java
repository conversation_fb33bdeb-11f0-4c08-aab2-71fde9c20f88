package com.tal.sea.seaover.application.controller;

import com.tal.sea.seaover.application.dto.EduEsQuestionResponse;
import com.tal.sea.seaover.application.dto.SimilarQuestionRequest;
import com.tal.sea.seaover.application.service.EsQuestionService;
import com.tal.sea.seaover.application.util.ResponseEntity;
import com.tal.sea.seaover.application.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * ES题目操作控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/question/es")
public class EsQuestionController {

    @Autowired
    private EsQuestionService esQuestionService;

    /**
     * 相似题推荐接口
     */
    @PostMapping("/similar")
    public ResponseEntity<List<EduEsQuestionResponse>> findSimilarQuestions(@RequestBody SimilarQuestionRequest request) {
        try {
            List<EduEsQuestionResponse> similarQuestions = esQuestionService.findSimilarQuestions(request);
            return ResponseUtil.successWithData(similarQuestions);
        } catch (Exception e) {
            log.error("相似题推荐异常", e);
            return ResponseUtil.failWith500("相似题推荐异常：" + e.getMessage());
        }
    }

}