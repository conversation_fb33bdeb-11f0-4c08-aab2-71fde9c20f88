package com.tal.sea.seaover.application.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 学科配置类
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "question.subjects")
public class SubjectConfig {
    
    /**
     * 学科列表
     */
    private List<SubjectItem> list;
    
    /**
     * 学科项
     */
    @Data
    public static class SubjectItem {
        /**
         * 学科名称
         */
        private String name;
        
        /**
         * 学科描述
         */
        private String description;
    }
}