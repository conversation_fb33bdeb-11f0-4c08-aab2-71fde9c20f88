package com.tal.sea.seaover.application.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传异常处理器
 */
@Slf4j
@RestControllerAdvice
public class FileUploadExceptionHandler {

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public Map<String, Object> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
        log.error("文件上传大小超过限制", e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "文件大小超过限制，最大支持100MB，请压缩文件后重试");
        response.put("totalRows", 0);
        response.put("validCount", 0);
        response.put("insertedCount", 0);
        response.put("filteredCount", 0);
        response.put("failedCount", 0);
        
        return response;
    }
}