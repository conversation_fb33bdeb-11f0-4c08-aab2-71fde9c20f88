package com.tal.sea.seaover.application.service.data;

import com.tal.sea.seaover.application.dto.ai.GeminiUsage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 使用ai大模型统计服务
 * 用于统计GeminiClientService#doChatCompletion方法中每次大模型返回的token使用情况
 */
@Slf4j
@Service
public class AiUsageStatisticsService {

    // 使用线程安全的数据结构来存储统计信息
    private final AtomicLong totalPromptTokens = new AtomicLong(0);
    private final AtomicLong totalCompletionTokens = new AtomicLong(0);
    private final AtomicLong totalTokens = new AtomicLong(0);
    private final AtomicInteger callCount = new AtomicInteger(0);

    // 存储最近的使用记录（可选，用于详细分析）
    private final ConcurrentLinkedQueue<GeminiUsageRecord> recentUsages = new ConcurrentLinkedQueue<>();
    private static final int MAX_RECENT_RECORDS = 1000; // 最多保存1000条最近记录

    /**
     * 记录一次Gemini API调用的token使用情况
     *
     * @param usage Gemini返回的使用统计
     */
    public void recordUsage(GeminiUsage usage) {
        if (usage == null) {
            log.warn("GeminiUsage为null，跳过统计");
            return;
        }

        // 累加总数
        totalPromptTokens.addAndGet(usage.getPromptTokens());
        totalCompletionTokens.addAndGet(usage.getCompletionTokens());
        totalTokens.addAndGet(usage.getTotalTokens());
        int currentCount = callCount.incrementAndGet();

        // 记录详细信息
        GeminiUsageRecord record = new GeminiUsageRecord(
                System.currentTimeMillis(),
                usage.getPromptTokens(),
                usage.getCompletionTokens(),
                usage.getTotalTokens()
        );

        recentUsages.offer(record);

        // 保持最近记录数量在限制范围内
        while (recentUsages.size() > MAX_RECENT_RECORDS) {
            recentUsages.poll();
        }

        log.debug("记录Gemini使用统计 - 调用次数: {}, 本次tokens: prompt={}, completion={}, total={}",
                currentCount, usage.getPromptTokens(), usage.getCompletionTokens(), usage.getTotalTokens());
    }

    /**
     * 获取平均使用统计
     *
     * @return 平均使用统计
     */
    public GeminiUsageStatistics getAverageStatistics() {
        int count = callCount.get();
        if (count == 0) {
            return new GeminiUsageStatistics(0, 0.0, 0.0, 0.0);
        }

        double avgPromptTokens = (double) totalPromptTokens.get() / count;
        double avgCompletionTokens = (double) totalCompletionTokens.get() / count;
        double avgTotalTokens = (double) totalTokens.get() / count;

        return new GeminiUsageStatistics(count, avgPromptTokens, avgCompletionTokens, avgTotalTokens);
    }

    /**
     * 获取总计使用统计
     *
     * @return 总计使用统计
     */
    public GeminiUsageTotals getTotalStatistics() {
        return new GeminiUsageTotals(
                callCount.get(),
                totalPromptTokens.get(),
                totalCompletionTokens.get(),
                totalTokens.get()
        );
    }

    /**
     * 重置统计数据
     */
    public void resetStatistics() {
        totalPromptTokens.set(0);
        totalCompletionTokens.set(0);
        totalTokens.set(0);
        callCount.set(0);
        recentUsages.clear();
        log.info("Gemini使用统计数据已重置");
    }


    /**
     * 使用记录内部类
     */
    private static class GeminiUsageRecord {
        private final long timestamp;
        private final int promptTokens;
        private final int completionTokens;
        private final int totalTokens;

        public GeminiUsageRecord(long timestamp, int promptTokens, int completionTokens, int totalTokens) {
            this.timestamp = timestamp;
            this.promptTokens = promptTokens;
            this.completionTokens = completionTokens;
            this.totalTokens = totalTokens;
        }

        // getters省略，仅用于内部存储
    }

    /**
     * 平均使用统计结果类
     */
    public static class GeminiUsageStatistics {
        private final int callCount;
        private final double avgPromptTokens;
        private final double avgCompletionTokens;
        private final double avgTotalTokens;

        public GeminiUsageStatistics(int callCount, double avgPromptTokens, double avgCompletionTokens, double avgTotalTokens) {
            this.callCount = callCount;
            this.avgPromptTokens = avgPromptTokens;
            this.avgCompletionTokens = avgCompletionTokens;
            this.avgTotalTokens = avgTotalTokens;
        }

        public int getCallCount() {
            return callCount;
        }

        public double getAvgPromptTokens() {
            return avgPromptTokens;
        }

        public double getAvgCompletionTokens() {
            return avgCompletionTokens;
        }

        public double getAvgTotalTokens() {
            return avgTotalTokens;
        }
    }

    /**
     * 总计使用统计结果类
     */
    public static class GeminiUsageTotals {
        private final int callCount;
        private final long totalPromptTokens;
        private final long totalCompletionTokens;
        private final long totalTokens;

        public GeminiUsageTotals(int callCount, long totalPromptTokens, long totalCompletionTokens, long totalTokens) {
            this.callCount = callCount;
            this.totalPromptTokens = totalPromptTokens;
            this.totalCompletionTokens = totalCompletionTokens;
            this.totalTokens = totalTokens;
        }

        public int getCallCount() {
            return callCount;
        }

        public long getTotalPromptTokens() {
            return totalPromptTokens;
        }

        public long getTotalCompletionTokens() {
            return totalCompletionTokens;
        }

        public long getTotalTokens() {
            return totalTokens;
        }
    }
}
