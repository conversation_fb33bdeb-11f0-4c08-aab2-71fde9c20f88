package com.tal.sea.seaover.application.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tal.sea.seaover.application.entity.EduQuestion;

import java.util.List;

/**
 * EduQuestion批量操作服务接口
 */
public interface EduQuestionBatchService extends IService<EduQuestion> {
    
    /**
     * 批量插入EduQuestion数据（默认每批500条）
     * 
     * @param questions 待插入的EduQuestion列表
     * @return 成功插入的总数量
     */
    int batchInsert(List<EduQuestion> questions);
}
