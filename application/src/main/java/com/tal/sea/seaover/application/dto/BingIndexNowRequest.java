package com.tal.sea.seaover.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 必应IndexNow API请求DTO
 */
@Data
public class BingIndexNowRequest {
    
    /**
     * 网站主机名
     */
    @JsonProperty("host")
    private String host = "www.edu.com";
    
    /**
     * API密钥
     */
    @JsonProperty("key")
    private String key = "c44738148c514d57be238a28b5e84b7e";
    
    /**
     * 密钥文件位置
     */
    @JsonProperty("keyLocation")
    private String keyLocation = "https://www.edu.com/c44738148c514d57be238a28b5e84b7e.txt";
    
    /**
     * 要上报的URL列表
     */
    @JsonProperty("urlList")
    private List<String> urlList;
}