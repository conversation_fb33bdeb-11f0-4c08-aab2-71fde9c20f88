package com.tal.sea.seaover.application.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * Gemini AI配置类
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "gemini")
public class GeminiConfig {
    
    /**
     * Gemini API URL
     */
    private String apiUrl;
    
    /**
     * Gemini模型名称
     */
    private String model;
    
    /**
     * API密钥
     */
    private String apiKey;
    
    /**
     * 提示语文件路径
     */
    private String promotionFile = "promotion/gemini-math-promotion_v2.txt";

    /**
     * 提示语文件路径
     */
    private String commentPromotionFile = "promotion/gemini-comment-promotion.txt";
}