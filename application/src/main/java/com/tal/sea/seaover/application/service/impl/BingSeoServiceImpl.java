package com.tal.sea.seaover.application.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tal.sea.seaover.application.dto.BingIndexNowRequest;
import com.tal.sea.seaover.application.service.BingSeoService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 必应SEO服务实现类
 */
@Slf4j
@Service
public class BingSeoServiceImpl implements BingSeoService {
    
    private static final String BING_INDEX_NOW_URL = "https://www.bing.com/indexnow";
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;
    
    public BingSeoServiceImpl() {
        // 配置OkHttpClient
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
        
        this.objectMapper = new ObjectMapper();
    }
    
    @Override
    public boolean reportUrlsToBing(List<String> urlList) {
        if (urlList == null || urlList.isEmpty()) {
            log.warn("URL列表为空，跳过上报");
            return false;
        }
        
        try {
            // 构建请求对象
            BingIndexNowRequest request = new BingIndexNowRequest();
            request.setUrlList(urlList);
            
            // 转换为JSON
            String jsonBody = objectMapper.writeValueAsString(request);
            log.info("准备上报{}个URL到必应IndexNow API", urlList.size());
            // 构建HTTP请求
            RequestBody body = RequestBody.create(jsonBody, JSON);
            Request httpRequest = new Request.Builder()
                    .url(BING_INDEX_NOW_URL)
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            
            // 执行请求
            try (Response response = httpClient.newCall(httpRequest).execute()) {
                int statusCode = response.code();
                String responseBody = response.body() != null ? response.body().string() : "";
                
                if (response.isSuccessful()) {
                    log.info("成功上报{}个URL到必应，状态码: {}", urlList.size(), statusCode);
                    if (!responseBody.isEmpty()) {
                        log.debug("响应内容: {}", responseBody);
                    }
                    return true;
                } else {
                    log.error("上报URL到必应失败，状态码: {}, 响应: {}", statusCode, responseBody);
                    return false;
                }
            }
            
        } catch (IOException e) {
            log.error("上报URL到必应时发生IO异常", e);
            return false;
        } catch (Exception e) {
            log.error("上报URL到必应时发生未知异常", e);
            return false;
        }
    }
}