package com.tal.sea.seaover.application.service.video;

import com.esotericsoftware.minlog.Log;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

@Service
public class ExcelProcessorService {

    @Autowired
    private AzureASRAudioSegService azureASRAudioSegService;

    private static final int THREAD_POOL_SIZE = Runtime.getRuntime().availableProcessors() * 2;

    public byte[] processExcel(InputStream excelInputStream, Path tempDir) throws Exception {
        Workbook workbook = new XSSFWorkbook(excelInputStream);
        try {
            // 添加结果列头
            addResultColumnHeader(workbook);

            // 处理每个sheet
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                processSheet(sheet, tempDir);
            }

            // 将处理后的Excel写入字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
        } finally {
            workbook.close();
        }
    }

    private void addResultColumnHeader(Workbook workbook) {
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheet = workbook.getSheetAt(i);
            if (sheet.getPhysicalNumberOfRows() > 0) {
                Row headerRow = sheet.getRow(0);
                if (headerRow != null) {
                    int lastCellNum = headerRow.getLastCellNum();
                    Cell resultHeaderCell = headerRow.createCell(lastCellNum);
                    resultHeaderCell.setCellValue("audio_segment_result");
                }
            }
        }
    }

    private void processSheet(Sheet sheet, Path tempDir) throws Exception {
        // 查找video_path列
        Row headerRow = sheet.getRow(0);
        if (headerRow == null) return;

        int videoPathColumnIndex = -1;
        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            Cell cell = headerRow.getCell(i);
            if (cell != null && "video_path".equals(cell.getStringCellValue())) {
                videoPathColumnIndex = i;
                break;
            }
        }

        if (videoPathColumnIndex == -1) return;

        // 创建线程池
        ExecutorService executor = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
        List<Future<?>> futures = new ArrayList<>();

        try {
            // 处理每一行数据
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                final int rowIndex = i;
                final int finalVideoPathColumnIndex = videoPathColumnIndex;

                // 提交任务到线程池
                futures.add(executor.submit(() -> {
                    try {
                        processRow(sheet, rowIndex, finalVideoPathColumnIndex, tempDir);
                    } catch (Exception e) {
                        // 日志记录异常
                        Log.error("Error processing row " + rowIndex + ": " + e.getMessage());
                    }
                }));
            }

            // 等待所有任务完成
            for (Future<?> future : futures) {
                future.get(30, TimeUnit.MINUTES); // 设置超时时间
            }
        } finally {
            executor.shutdown();
            executor.awaitTermination(50, TimeUnit.MINUTES);
        }
    }

    private void processRow(Sheet sheet, int rowIndex, int videoPathColumnIndex, Path tempDir) throws Exception {
        Row row = sheet.getRow(rowIndex);
        if (row == null) return;

        Cell videoPathCell = row.getCell(videoPathColumnIndex);
        if (videoPathCell == null) return;

        String videoPath = getCellValueAsString(videoPathCell);
        if (videoPath == null || videoPath.trim().isEmpty()) return;

        // 检查是否为有效路径
        if (!isValidUrl(videoPath)) return;

        // 处理URL前缀
        String processedVideoPath = processVideoPath(videoPath);

        // 下载视频文件
        String videoFileName = UUID.randomUUID() + ".mp4";
        Path videoFilePath = tempDir.resolve(videoFileName);
        if (!downloadFile(processedVideoPath, videoFilePath)) return;

        try {
            // 生成音频文件
            String audioFileName = videoFileName.replace(".mp4", ".wav");
            Path audioFilePath = tempDir.resolve(audioFileName);
            extractAudio(videoFilePath, audioFilePath);

            // 解析音频文件
            String result = azureASRAudioSegService.getAudioSegmentPlus(audioFilePath.toRealPath().toString());

            // 同步写入Excel
            synchronized (sheet) {
                int resultColumnIndex = row.getLastCellNum();
                Cell resultCell = row.createCell(resultColumnIndex);
                resultCell.setCellValue(result);
            }
        } finally {
            // 删除临时文件
            try {
                Files.deleteIfExists(videoFilePath);
                Path audioFilePath = tempDir.resolve(videoFileName.replace(".mp4", ".wav"));
                Files.deleteIfExists(audioFilePath);
            } catch (Exception e) {
                // 忽略删除异常
            }
        }
    }

    private String getCellValueAsString(Cell cell) {
        if (cell == null) return null;
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            default:
                return null;
        }
    }

    private boolean isValidUrl(String url) {
        try {
            new URL(url);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private String processVideoPath(String videoPath) {
        if (!videoPath.startsWith("https://cfcdn.thinkbuddy.com")) {
            String path = videoPath;
            if (videoPath.startsWith("http://")) {
                path = videoPath.substring(7);
            } else if (videoPath.startsWith("https://")) {
                path = videoPath.substring(8);
            }

            int firstSlash = path.indexOf("/");
            if (firstSlash != -1) {
                path = path.substring(firstSlash);
            } else {
                path = "/" + path;
            }

            return "https://cfcdn.thinkbuddy.com" + path;
        }
        return videoPath;
    }

    private boolean downloadFile(String fileUrl, Path filePath) {
        try {
            URL url = new URL(fileUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(10000);

            if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                try (InputStream in = connection.getInputStream();
                     FileOutputStream out = new FileOutputStream(filePath.toFile())) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                    }
                }
                return true;
            }
        } catch (Exception e) {
            // 下载失败
        }
        return false;
    }

    private void extractAudio(Path videoFilePath, Path audioFilePath) throws Exception {
        ProcessBuilder processBuilder = new ProcessBuilder(
                "ffmpeg",
                "-i",
                videoFilePath.toString(),
                "-vn",
                "-c:a",
                "pcm_s16le",
                audioFilePath.toString()
        );

        Process process = processBuilder.start();
        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new RuntimeException("FFmpeg处理失败，退出码: " + exitCode);
        }
    }
}