package com.tal.sea.seaover.application.service.data;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * 问题OpenAI处理服务
 * 类似于VideoAudioProcessorService的结构
 */
@Slf4j
@Service
public class QuestionOpenAIProcessorService {

    @Autowired
    private OpenAIExcelProcessorService openAIExcelProcessorService;

    /**
     * 处理Excel文件
     * 读取question_text列，调用OpenAI API处理，并将结果写入最后一列
     * 
     * @param file 上传的Excel文件
     * @return 处理后的Excel文件字节数组
     * @throws Exception 处理异常
     */
    public byte[] processExcelFile(MultipartFile file) throws Exception {
        log.info("开始处理Excel文件: {}, 大小: {} bytes", file.getOriginalFilename(), file.getSize());
        
        // 验证文件类型
        if (!isValidExcelFile(file)) {
            throw new IllegalArgumentException("不支持的文件类型，请上传.xlsx格式的Excel文件");
        }
        
        try {
            // 调用Excel处理服务
            byte[] result = openAIExcelProcessorService.processExcel(file.getInputStream());
            
            log.info("Excel文件处理完成: {}", file.getOriginalFilename());
            return result;
            
        } catch (Exception e) {
            log.error("处理Excel文件失败: {}", file.getOriginalFilename(), e);
            throw new Exception("处理Excel文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证是否为有效的Excel文件
     */
    private boolean isValidExcelFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }
        
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            return false;
        }
        
        // 检查文件扩展名
        String lowerCaseFilename = originalFilename.toLowerCase();
        if (!lowerCaseFilename.endsWith(".xlsx") && !lowerCaseFilename.endsWith(".xls")) {
            return false;
        }
        
        // 检查MIME类型
        String contentType = file.getContentType();
        return contentType != null && (
            contentType.equals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") ||
            contentType.equals("application/vnd.ms-excel") ||
            contentType.equals("application/octet-stream")
        );
    }
}