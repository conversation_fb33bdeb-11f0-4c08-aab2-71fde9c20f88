package com.tal.sea.seaover.application.service.comment;

import com.tal.sea.seaover.application.entity.EduQuestion;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CommentsService {

    @Async
    public void asyncGenerateComments(EduQuestion question) {
        log.info("开始异步生成评论");

    }
}
