package com.tal.sea.seaover.application.service.comment;

import com.alibaba.nacos.common.utils.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tal.sea.seaover.application.entity.Comments;
import com.tal.sea.seaover.application.entity.EduQuestion;
import com.tal.sea.seaover.application.mapper.CommentsMapper;
import com.tal.sea.seaover.application.mapper.EduQuestionMapper;
import com.tal.sea.seaover.application.service.EduQuestionBatchService;
import com.tal.sea.seaover.application.service.EduQuestionService;
import com.tal.sea.seaover.application.service.data.GeminiClientService;
import com.tal.sea.seaover.application.util.QuestionCommentUtil;
import com.tal.sea.seaover.application.util.ResponseEntity;
import com.tal.sea.seaover.application.util.ResponseUtil;
import com.tal.sea.xpod.tools.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CommentsService {

    @Autowired
    private GeminiClientService geminiClientService;

    @Autowired
    private CommentsMapper commentsMapper;

    @Autowired
    private EduQuestionMapper eduQuestionMapper;
    @Autowired
    private CommentsBatchService commentsBatchService;

    @Autowired
    private ThreadPoolTaskExecutor commentsTaskExecutor;

    @Autowired
    private EduQuestionService eduQuestionService;
    @Autowired
    private EduQuestionBatchService eduQuestionBatchService;


    @Async("commentsTaskExecutor")
    public void generateComments(List<EduQuestion> questions) {
        if (CollectionUtils.isEmpty(questions)) {
            log.info("生成评论任务：试题列表为空，退出任务");
            return;
        }
        List<Future<List<Comments>>> futures = new ArrayList<>();
        for (EduQuestion question : questions) {
            Future<List<Comments>> future = commentsTaskExecutor.submit(() -> {
                List<Comments> questionAllComment = new ArrayList<>();
                Set<String> usedUsernames = new HashSet<>(); // 存储已使用的用户名
                try {
                    //每个试题生成3次评论
                    for (int i = 1; i <= 3; i++) {
                        long start = System.currentTimeMillis();
                        Comments comment = generateSingleCommentWithRetry(question, i, 3);
                        long end = System.currentTimeMillis();
                        log.info("生成评论耗时: {}ms", end - start);
                        if (comment != null) {
                            String username = comment.getUserName();
                            while (usedUsernames.contains(username)) {
                                // 如果用户名重复，调用 getRandomUserName 获取新用户名
                                username = QuestionCommentUtil.getRandomUserName();
                                comment.setUserName(username);
                            }
                            usedUsernames.add(username);
                            questionAllComment.add(comment);
                        }
                    }
                    return questionAllComment;
                } catch (Exception e) {
                    log.error("生成评论过程中发生异常，试题ID: {}, 错误信息: {}", question.getId(), e.getMessage(), e);
                }
                return questionAllComment;
            });
            futures.add(future);
        }

        List<Comments> successQuestions = new ArrayList<>();
        for (Future<List<Comments>> future : futures) {
            try {
                List<Comments> result = future.get(10, TimeUnit.MINUTES); // 设置超时时间
                if (result != null) {
                    successQuestions.addAll(result);
                }
            } catch (TimeoutException e) {
                log.error("生成题目评价超时", e);
                future.cancel(true);
            } catch (Exception e) {
                log.error("获取题目生成的评价失败", e);
            }
        }

        // 批量插入评论
        if (!CollectionUtils.isEmpty(successQuestions)) {
            commentsBatchService.batchInsert(successQuestions);
        }
    }

    /**
     * 异步生成评论
     * 循环调用三次，生成三个评论，支持重试机制和批量插入
     *
     * @param question 试题对象
     */
    public void generateComments(EduQuestion question) {
        try {
            List<Comments> commentsToInsert = new ArrayList<>();

            // 循环生成三个评论
            for (int i = 1; i <= 3; i++) {
                Comments comment = generateSingleCommentWithRetry(question, i, 3);
                if (comment != null) {
                    commentsToInsert.add(comment);
                }
            }

            // 批量插入评论
            if (!commentsToInsert.isEmpty()) {
                commentsBatchService.batchInsert(commentsToInsert);
            } else {
                log.warn("没有成功生成任何评论，试题ID: {}", question.getId());
            }
        } catch (Exception e) {
            log.error("生成评论过程中发生异常，试题ID: {}, 错误信息: {}", question.getId(), e.getMessage(), e);
        }
    }


    /**
     * 生成单个评论，支持重试机制
     *
     * @param question     试题对象
     * @param commentIndex 评论序号
     * @param maxRetries   最大重试次数
     * @return 生成的评论对象，失败时返回null
     */
    private Comments generateSingleCommentWithRetry(EduQuestion question, int commentIndex, int maxRetries) {
        for (int retry = 1; retry <= maxRetries; retry++) {
            try {
                // 1. 调用GeminiClientService获得大模型结果
                String geminiResult = geminiClientService.chatCompletionForComment(question.getQuestionText());

                if (StringUtils.isEmpty(geminiResult)) {
                    log.warn("评论生成失败：大模型返回结果为空，试题ID: {}, 第{}次尝试", question.getId(), retry);
                    if (retry < maxRetries) {
                        continue;
                    } else {
                        return null;
                    }
                }

                // 2. 调用QuestionCommentUtil解析用户名和评价内容
                QuestionCommentUtil.QuestionComment questionComment = QuestionCommentUtil.parseComment(geminiResult);

                if (StringUtils.isEmpty(questionComment.userName()) ||
                        StringUtils.isEmpty(questionComment.comment())) {
                    log.warn("第{}个评论解析失败：用户名或评论内容为空，试题ID: {}, 第{}次尝试",
                            commentIndex, question.getId(), retry);
                    if (retry < maxRetries) {
                        continue;
                    } else {
                        return null;
                    }
                }

                // 3. 创建Comments对象
                return createComments(question, commentIndex, questionComment);
            } catch (Exception e) {
                log.error("生成第{}个评论时发生异常，试题ID: {}, 第{}次尝试, 错误信息: {}",
                        commentIndex, question.getId(), retry, e.getMessage(), e);
                if (retry >= maxRetries) {
                    log.error("第{}个评论生成失败，已达到最大重试次数{}，试题ID: {}",
                            commentIndex, maxRetries, question.getId());
                    return null;
                }
            }
        }
        return null;
    }


    private Comments createComments(EduQuestion question, int commentIndex,
                                    QuestionCommentUtil.QuestionComment questionComment) {
        Comments comment = new Comments();
        comment.setSubjectType(6); // 固定为6，表示question类型
        comment.setSubjectId(String.valueOf(question.getId())); // 试题ID
        comment.setParentId(0L); // 固定为0
        comment.setUserAvatar(""); // 空字符串
        comment.setUserName(questionComment.userName().trim()); // 解析得到的用户名
        comment.setTalId(""); // 空字符串
        comment.setContent(questionComment.comment().trim()); // 解析得到的评价内容

        // 4. 设置创建时间：第一次是当前时间+7天，第二次是上次创建时间+7天，依次类推
        Date createdAt = calculateCreatedAt(commentIndex);
        comment.setCreatedAt(createdAt);
        return comment;
    }

    /**
     * 计算创建时间
     * 第一次的创建时间是当前时间+7天
     * 第二次是上次创建时间的+7天
     * 依次类推
     *
     * @param commentIndex 评论序号（1, 2, 3）
     * @return 计算后的创建时间
     */
    private Date calculateCreatedAt(int commentIndex) {
        Calendar calendar = Calendar.getInstance();

        // 第一次：当前时间 + 7天
        // 第二次：当前时间 + 14天
        // 第三次：当前时间 + 21天
        int daysToAdd = commentIndex * 7;
        calendar.add(Calendar.DAY_OF_MONTH, daysToAdd);

        return calendar.getTime();
    }

    /**
     * 手动为指定试题生成评论
     *
     * @param questionId 试题ID
     * @return 生成结果
     */
    public ResponseEntity generateCommentsForQuestion(Long questionId) {
        try {
            // 查询试题是否存在
            EduQuestion question = eduQuestionMapper.selectById(questionId);
            if (question == null) {
                return ResponseUtil.failErrorParam("试题不存在，ID: " + questionId);
            }
            // 生成评论
            generateComments(question);
            log.info("手动触发评论生成，试题ID: {}", questionId);
            return ResponseUtil.successWithoutData();
        } catch (Exception e) {
            log.error("手动生成评论时发生异常，试题ID: {}", questionId, e);
            return ResponseUtil.failWith500("生成评论失败：" + e.getMessage());
        }
    }

    /**
     * 查询指定试题的评论
     *
     * @param questionId 试题ID
     * @return 评论列表
     */
    public ResponseEntity getCommentsByQuestionId(Long questionId) {
        try {
            QueryWrapper<Comments> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("subject_type", 6)
                    .eq("subject_id", String.valueOf(questionId))
                    .orderByAsc("created_at");

            List<Comments> comments = commentsMapper.selectList(queryWrapper);

            Map<String, Object> data = new HashMap<>();
            data.put("questionId", questionId);
            data.put("commentsCount", comments.size());
            data.put("comments", comments);

            log.info("查询试题评论，试题ID: {}, 评论数: {}", questionId, comments.size());
            return ResponseUtil.successWithData(data);

        } catch (Exception e) {
            log.error("查询试题评论时发生异常，试题ID: {}", questionId, e);
            return ResponseUtil.failWith500("查询评论失败：" + e.getMessage());
        }
    }

    /**
     * 删除指定试题的所有评论
     *
     * @param questionId 试题ID
     * @return 删除结果
     */
    public ResponseEntity deleteCommentsByQuestionId(Long questionId) {
        try {
            QueryWrapper<Comments> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("subject_type", 6)
                    .eq("subject_id", String.valueOf(questionId));

            int deletedCount = commentsMapper.delete(queryWrapper);

            Map<String, Object> data = new HashMap<>();
            data.put("questionId", questionId);
            data.put("deletedCount", deletedCount);

            log.info("删除试题评论，试题ID: {}, 删除数量: {}", questionId, deletedCount);
            return ResponseUtil.successWithData(data);

        } catch (Exception e) {
            log.error("删除试题评论时发生异常，试题ID: {}", questionId, e);
            return ResponseUtil.failWith500("删除评论失败：" + e.getMessage());
        }
    }

    /**
     * 根据评论时间更新试题的修改时间
     * 查询最近24小时内的评论，如果评论时间晚于试题的修改时间，则更新试题的修改时间
     *
     * @return 更新结果
     */
    public ResponseEntity updateQuestionModifyTime() {
        try {
            Date currentTime = new Date();
            Date startTime = Date.from(currentTime.toInstant().minus(Duration.ofHours(24))); // 当前时间减6小时

            log.info("开始更新试题修改时间，查询评论时间范围: {} 到 {}",
                    DateUtil.formatDate(startTime, DateUtil.FMT_DATE_YYYYMMDD_HHMMSS_STR),
                    DateUtil.formatDate(currentTime, DateUtil.FMT_DATE_YYYYMMDD_HHMMSS_STR));

            int totalProcessed = 0;
            int totalUpdated = 0;
            int currentPage = 1;
            int pageSize = 500;
            boolean hasMoreData = true;

            while (hasMoreData) {
                // 分页查询评论
                Page<Comments> commentsPage = listCommentsPage(currentPage, pageSize, startTime, currentTime);
                List<Comments> comments = commentsPage.getRecords();
                if (comments.isEmpty()) {
                    log.info("没有更多评论数据需要处理，退出循环");
                    break;
                }

                log.info("第{}页，查询到{}条评论", currentPage, comments.size());

                // 按试题ID分组处理
                Map<String, List<Comments>> commentsByQuestionId = comments.stream()
                        .collect(Collectors.groupingBy(Comments::getSubjectId));

                int pageUpdated = processQuestionUpdates(commentsByQuestionId);
                totalUpdated += pageUpdated;
                totalProcessed += comments.size();

                log.info("第{}页处理完成，处理评论数: {}, 更新试题数: {}", currentPage, comments.size(), pageUpdated);

                hasMoreData = commentsPage.hasNext();
                currentPage++;
            }
            log.info("试题修改时间更新完成，总处理评论数: {}, 总更新试题数: {}", totalProcessed, totalUpdated);
            return ResponseUtil.successWithoutData();

        } catch (Exception e) {
            log.error("更新试题修改时间时发生异常", e);
            return ResponseUtil.failWith500("更新试题修改时间失败：" + e.getMessage());
        }
    }

    private Page<Comments> listCommentsPage(int currentPage, int pageSize, Date startTime, Date currentTime) {
        Page<Comments> page = new Page<>(currentPage, pageSize);
        QueryWrapper<Comments> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("subject_type", 6)
                .isNotNull("content")
                .ne("content", "")
                .between("created_at", startTime, currentTime)
                .orderByDesc("created_at", "id");
        return commentsMapper.selectPage(page, queryWrapper);
    }

    /**
     * 处理试题更新时间的批量更新
     *
     * @param commentsByQuestionId 按试题ID分组的评论
     * @return 更新的试题数量
     */
    private int processQuestionUpdates(Map<String, List<Comments>> commentsByQuestionId) {
        // 1. 提取有效的试题ID
        List<String> questionIds = new ArrayList<>(commentsByQuestionId.keySet());
        if (questionIds.isEmpty()) {
            return 0;
        }
        // 2. 批量查询试题
        List<EduQuestion> questions = eduQuestionService.batchQueryByIds(questionIds);
        Map<Long, EduQuestion> questionMap = questions.stream()
                .collect(Collectors.toMap(EduQuestion::getId, question -> question));
        if (MapUtil.isEmpty(questionMap)) {
            return 0;
        }
        // 3. 确定需要更新的试题和最新评论时间
        List<EduQuestion> questionsToUpdate = new ArrayList<>();
        for (Map.Entry<String, List<Comments>> entry : commentsByQuestionId.entrySet()) {
            try {
                Long questionId = Long.valueOf(entry.getKey());
                EduQuestion question = questionMap.get(questionId);
                if (question == null) {
                    log.info("试题不存在，跳过更新，试题ID: {}", questionId);
                    continue;
                }

                // 找到最晚的评论时间
                Date latestCommentTime = entry.getValue().stream()
                        .map(Comments::getCreatedAt)
                        .filter(Objects::nonNull)
                        .max(Date::compareTo)
                        .orElse(null);

                if (latestCommentTime == null) {
                    continue;
                }

                // 判断是否需要更新
                Date questionUpdateTime = question.getUpdateTime();
                if (questionUpdateTime != null && latestCommentTime.after(questionUpdateTime)) {
                    question.setUpdateTime(latestCommentTime);
                    questionsToUpdate.add(question);
                }
            } catch (NumberFormatException e) {
                log.warn("试题ID格式错误，跳过处理: {}", entry.getKey());
            }
        }
        eduQuestionBatchService.updateBatchById(questionsToUpdate);
        // 4. 批量更新
        return questionsToUpdate.size();
    }
}
