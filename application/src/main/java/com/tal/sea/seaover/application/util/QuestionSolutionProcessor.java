package com.tal.sea.seaover.application.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class QuestionSolutionProcessor {
    /**
     * 处理试题解析
     *
     * @param questionSolution
     * @return
     */
    public static String processSteps(String questionSolution) {
        if (StringUtils.isEmpty(questionSolution)) {
            return questionSolution;
        }
        // Check if questionSolution contains any line matching "QuestionX.stepY (Content)"
        Pattern checkPattern = Pattern.compile("Question\\d+\\.step\\d+\\s*\\([^)]+\\)");
        boolean hasValidPattern = false;
        for (String line : questionSolution.split("\n")) {
            if (checkPattern.matcher(line.trim()).matches()) {
                hasValidPattern = true;
                break;
            }
        }

        // If no valid pattern found, return original questionSolution
        if (!hasValidPattern) {
            return questionSolution;
        }

        // Split questionSolution into lines
        String[] lines = questionSolution.split("\n");
        StringBuilder result = new StringBuilder();

        // Pattern to match "QuestionX.stepY (Content)"
        Pattern pattern = Pattern.compile("Question\\d+\\.step(\\d+)\\s*\\(([^)]+)\\)");

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();
            // Skip empty lines
            if (line.isEmpty()) {
                continue;
            }

            // Check if line matches the pattern
            Matcher matcher = pattern.matcher(line);
            if (matcher.matches()) {
                // Extract step number and content
                String stepNumber = matcher.group(1);
                String content = matcher.group(2);
                // Format as **stepX** Content
                result.append("**step").append(stepNumber).append("**  ").append(content).append("  \n");
                // Add newline after step line if it's not the last line and not followed by <step>
                if (i < lines.length - 1 && !lines[i + 1].trim().startsWith("<step>")) {
                    result.append("\n");
                }
            } else {
                // Preserve <step> content as is and add newline after </step>
                result.append(line).append("\n");
                if (line.trim().endsWith("</step>") && i < lines.length - 1) {
                    result.append("\n");
                }
            }
        }

        // Remove trailing newline if present
        if (!result.isEmpty() && result.charAt(result.length() - 1) == '\n') {
            result.setLength(result.length() - 1);
        }

        return result.toString();
    }

    /**
     * 验证solution格式是否符合要求
     *
     * @param solution 解析内容
     * @return true-符合要求，false-不符合要求
     */
    public static boolean validateSolution(long questionId, String solution) {
        if (StringUtils.isEmpty(solution)) {
            log.info("试题ID:{} Solution为空，验证失败", questionId);
            return false;
        }

        // 1. 检查是否以**step1**开头
        if (!isStartWithStep1(solution)) {
            log.info("试题ID:{} Solution不是以**step1**开头，验证失败", questionId);
            return false;
        }

        // 2. 检查数学公式是否被双$$包裹
        if (!isMathFormulaProperlyWrapped(solution)) {
            log.info("试题ID:{} Solution中数学公式未被双$$正确包裹，验证失败", questionId);
            return false;
        }

        // 3. 检查是否有重复步骤
        if (hasDuplicateSteps(solution)) {
            log.info("试题ID:{} Solution中存在重复步骤，验证失败", questionId);
            return false;
        }

        // 4. 检查是否包含连续三个---
        if (hasConsecutiveDashes(solution)) {
            log.info("试题ID:{} Solution中包含连续三个---(加粗)，验证失败", questionId);
            return false;
        }
        return true;
    }

    /**
     * 检查是否以**step1**开头
     */
    private static boolean isStartWithStep1(String solution) {
        String trimmed = solution.trim();
        return trimmed.toLowerCase().startsWith("**step1**") ||
                trimmed.toLowerCase().startsWith("**step 1**");
    }

    /**
     * 检查数学公式是否被双$$包裹
     * 核心规则：
     * 1. $$必须成对出现
     * 2. $可以作为货币符号使用
     * 3. $$内容$$中的内容不需要再进行验证
     */
    private static boolean isMathFormulaProperlyWrapped(String solution) {
        // 1. 检查$$必须成对出现
        int doubleDollarCount = 0;
        int index = 0;
        while ((index = solution.indexOf("$$", index)) != -1) {
            doubleDollarCount++;
            index += 2;
        }
        if (doubleDollarCount % 2 != 0) {
            log.info("发现未配对的$$符号，数量：{}", doubleDollarCount);
            return false;
        }

        // 2. 移除所有双$$包裹的内容（内容不需要验证，直接放行）
        // 修正：使用更准确的正则表达式，支持双$$内包含任何字符（包括$）
        String withoutDoubleDollar = solution.replaceAll("\\$\\$.*?\\$\\$", "");

        // 3. 移除所有货币符号（$可以作为货币符号使用）
        String currencyPattern = "\\$\\d+(?:,\\d{3})*(?:\\.\\d+)?(?!\\w)";
        String withoutCurrency = withoutDoubleDollar.replaceAll(currencyPattern, "");

        // 4. 检查是否还有剩余的$符号
        int remainingDollarCount = 0;
        for (char c : withoutCurrency.toCharArray()) {
            if (c == '$') {
                remainingDollarCount++;
            }
        }

        // 如果还有剩余的$符号，说明存在非货币、非双$$包裹的$符号
        if (remainingDollarCount > 0) {
            log.info("发现{}个非货币且未被双$$包裹的$符号", remainingDollarCount);
            return false;
        }

        return true;
    }






    /**
     * 检查是否有重复步骤
     */
    private static boolean hasDuplicateSteps(String solution) {
        // 使用正则表达式查找所有步骤标记
        String stepPattern = "\\*\\*step\\s*(\\d+)\\*\\*";
        Pattern pattern = Pattern.compile(stepPattern, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(solution);

        Set<String> stepNumbers = new HashSet<>();
        while (matcher.find()) {
            String stepNumber = matcher.group(1);
            if (stepNumbers.contains(stepNumber)) {
                return true;
            }
            stepNumbers.add(stepNumber);
        }

        return false;
    }

    /**
     * 检查是否包含连续三个---
     */
    private static boolean hasConsecutiveDashes(String solution) {
        return solution.contains("---");
    }

    public static void main(String[] args) {
        // 测试您选中的代码
        String testSolution = "$$560 \\text{ people} \\times \\$3/\\text{student} = \\$1680$$";
        System.out.println("=== 测试您选中的案例 ===");
        System.out.println("测试内容: " + testSolution);
        System.out.println("结果: " + isMathFormulaProperlyWrapped(testSolution));

        // 详细分析
        System.out.println("\n=== 详细分析 ===");

        // 1. 检查$$配对
        int doubleDollarCount = 0;
        int index = 0;
        while ((index = testSolution.indexOf("$$", index)) != -1) {
            doubleDollarCount++;
            index += 2;
        }
        System.out.println("1. $$符号数量: " + doubleDollarCount + " (成对: " + (doubleDollarCount % 2 == 0) + ")");

        // 2. 移除双$$内容
        String withoutDoubleDollar = testSolution.replaceAll("\\$\\$.*?\\$\\$", "");
        System.out.println("2. 移除双$$后: '" + withoutDoubleDollar + "'");

        // 3. 移除货币符号
        String currencyPattern = "\\$\\d+(?:,\\d{3})*(?:\\.\\d+)?(?!\\w)";
        String withoutCurrency = withoutDoubleDollar.replaceAll(currencyPattern, "");
        System.out.println("3. 移除货币符号后: '" + withoutCurrency + "'");

        // 4. 统计剩余$符号
        int remainingDollarCount = 0;
        for (char c : withoutCurrency.toCharArray()) {
            if (c == '$') {
                remainingDollarCount++;
            }
        }
        System.out.println("4. 剩余$符号数量: " + remainingDollarCount);

        // 测试其他案例
        System.out.println("\n=== 其他测试案例 ===");
        String[] testCases = {
            "Price is $10.75",                                    // 只有货币
            "Formula $$x^2 + y^2 = z^2$$",                       // 只有双$$
            "Price $10 and formula $$x = 1$$",                   // 货币+双$$
            "Variable $x$ is important",                         // 单$变量
            "Formula $$f($x$) = $x$ + 1$$",                      // 双$$内包含$
            "Unpaired $$x^2 + y^2$"                             // 未配对$$
        };

        for (int i = 0; i < testCases.length; i++) {
            boolean result = isMathFormulaProperlyWrapped(testCases[i]);
            System.out.println((i+1) + ". " + result + " - " + testCases[i]);
        }
    }
}
