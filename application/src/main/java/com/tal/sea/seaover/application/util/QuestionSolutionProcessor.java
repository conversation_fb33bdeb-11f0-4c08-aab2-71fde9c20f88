package com.tal.sea.seaover.application.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class QuestionSolutionProcessor {
    /**
     * 处理试题解析
     *
     * @param questionSolution
     * @return
     */
    @Deprecated
    public static String processSteps(String questionSolution) {
        if (StringUtils.isEmpty(questionSolution)) {
            return questionSolution;
        }
        // Check if questionSolution contains any line matching "QuestionX.stepY (Content)"
        Pattern checkPattern = Pattern.compile("Question\\d+\\.step\\d+\\s*\\([^)]+\\)");
        boolean hasValidPattern = false;
        for (String line : questionSolution.split("\n")) {
            if (checkPattern.matcher(line.trim()).matches()) {
                hasValidPattern = true;
                break;
            }
        }

        // If no valid pattern found, return original questionSolution
        if (!hasValidPattern) {
            return questionSolution;
        }

        // Split questionSolution into lines
        String[] lines = questionSolution.split("\n");
        StringBuilder result = new StringBuilder();

        // Pattern to match "QuestionX.stepY (Content)"
        Pattern pattern = Pattern.compile("Question\\d+\\.step(\\d+)\\s*\\(([^)]+)\\)");

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();
            // Skip empty lines
            if (line.isEmpty()) {
                continue;
            }

            // Check if line matches the pattern
            Matcher matcher = pattern.matcher(line);
            if (matcher.matches()) {
                // Extract step number and content
                String stepNumber = matcher.group(1);
                String content = matcher.group(2);
                // Format as **stepX** Content
                result.append("**step").append(stepNumber).append("**  ").append(content).append("  \n");
                // Add newline after step line if it's not the last line and not followed by <step>
                if (i < lines.length - 1 && !lines[i + 1].trim().startsWith("<step>")) {
                    result.append("\n");
                }
            } else {
                // Preserve <step> content as is and add newline after </step>
                result.append(line).append("\n");
                if (line.trim().endsWith("</step>") && i < lines.length - 1) {
                    result.append("\n");
                }
            }
        }

        // Remove trailing newline if present
        if (!result.isEmpty() && result.charAt(result.length() - 1) == '\n') {
            result.setLength(result.length() - 1);
        }

        return result.toString();
    }

    /**
     * 验证solution格式是否符合要求
     *
     * @param solution 解析内容
     * @return true-符合要求，false-不符合要求
     */
    public static boolean validateSolution(long questionId, String solution) {
        if (StringUtils.isEmpty(solution)) {
            log.info("试题ID:{} Solution为空，验证失败", questionId);
            return false;
        }

        // 1. 检查是否以正常开头
        if (!isStartWithStep1(solution)) {
            log.info("试题ID:{} Solution不是以**step 或者## question 开头，验证失败", questionId);
            return false;
        }

        // 2. 检查数学公式是否被双$$包裹
        if (!isMathFormulaProperlyWrapped(solution)) {
            log.info("试题ID:{} Solution中数学公式未被双$$正确包裹，验证失败", questionId);
            return false;
        }

        // 3. 检查是否包含连续三个---
        if (hasConsecutiveDashes(solution)) {
            log.info("试题ID:{} Solution中包含连续三个---(加粗)，验证失败", questionId);
            return false;
        }
        return true;
    }

    /**
     * 验证字符串开头
     */
    private static boolean isStartWithStep1(String solution) {
        String trimmed = solution.trim().toLowerCase();
        return trimmed.startsWith("**step") ||
                trimmed.startsWith("## question");
    }

    /**
     * 检查数学公式是否被双$$包裹
     * 核心规则：
     * 1. $$必须成对出现
     * 2. $可以作为货币符号使用
     * 3. $$内容$$中的内容不需要再进行验证
     */
    private static boolean isMathFormulaProperlyWrapped(String solution) {
        // 1. 检查$$必须成对出现
        int doubleDollarCount = 0;
        int index = 0;
        while ((index = solution.indexOf("$$", index)) != -1) {
            doubleDollarCount++;
            index += 2;
        }
        if (doubleDollarCount % 2 != 0) {
            log.info("发现未配对的$$符号，数量：{}", doubleDollarCount);
            return false;
        }

        // 2. 移除所有双$$包裹的内容（内容不需要验证，直接放行）
        // 修正：使用更准确的正则表达式，支持双$$内包含任何字符（包括$）
        String withoutDoubleDollar = solution.replaceAll("\\$\\$.*?\\$\\$", "");

        // 3. 移除所有货币符号（$可以作为货币符号使用）
        String currencyPattern = "\\$\\d+(?:,\\d{3})*(?:\\.\\d+)?(?!\\w)";
        String withoutCurrency = withoutDoubleDollar.replaceAll(currencyPattern, "");

        // 4. 检查是否还有剩余的$符号
        int remainingDollarCount = 0;
        for (char c : withoutCurrency.toCharArray()) {
            if (c == '$') {
                remainingDollarCount++;
            }
        }

        // 如果还有剩余的$符号，说明存在非货币、非双$$包裹的$符号
        if (remainingDollarCount > 0) {
            log.info("发现{}个非货币且未被双$$包裹的$符号", remainingDollarCount);
            return false;
        }

        return true;
    }


    /**
     * 检查是否包含连续三个---
     */
    private static boolean hasConsecutiveDashes(String solution) {
        return solution.contains("---");
    }

    public static void main(String[] args) {
        String str= """
                **step1 Count the Total Number of Letters**
                <step>
                <text>First, identify the total number of letters in the given word "ALGEBRA".</text>
                <formula>Total number of letters (n) = 7</formula>
                </step>
                
                **step2 Identify Repeated Letters and Their Frequencies**
                <step>
                <text>Next, observe if any letters are repeated and count how many times each repeated letter appears. In the word "ALGEBRA", the letter 'A' appears more than once.</text>
                <formula>Frequency of 'A' = 2</formula>
                </step>
                
                **step3 Apply the Permutation Formula for Repeated Items**
                <step>
                <text>To find the number of unique permutations for a word with repeated letters, we use the formula: $ \\frac{n!}{n_1! n_2! ... n_k!} $, where 'n' is the total number of letters, and $n_1, n_2, ..., n_k$ are the frequencies of the repeated letters. In this case, n=7 and the letter 'A' is repeated 2 times.</text>
                <formula>$$ \\text{Number of unique permutations} = \\frac{7!}{2!} $$</formula>
                </step>
                
                **step4 Calculate the Factorials**
                <step>
                <text>Calculate the values of the factorials in the formula.</text>
                <formula>$$ 7! = 7 \\times 6 \\times 5 \\times 4 \\times 3 \\times 2 \\times 1 = 5040 $$</formula>
                <formula>$$ 2! = 2 \\times 1 = 2 $$</formula>
                </step>
                
                **step5 Calculate the Final Number of Unique Permutations**
                <step>
                <text>Divide the factorial of the total number of letters by the factorial of the frequency of the repeated letter(s).</text>
                <formula>$$ \\text{Number of unique permutations} = \\frac{5040}{2} = 2520 $$</formula>
                </step>
                """;
        boolean mathFormulaProperlyWrapped = isMathFormulaProperlyWrapped(str);
        System.out.println(mathFormulaProperlyWrapped);
    }

}
